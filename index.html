<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' https://unpkg.com; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com; font-src 'self'; img-src 'self' data: https://*.tile.openstreetmap.org"
    />
    <title>SMCA GSA Dashboard</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="loading-indicator.css" />
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/leaflet/leaflet.css" />
    <!-- Font Awesome for icons (local) -->
    <link rel="stylesheet" href="assets/fontawesome/css/font-face.css" />
    <link rel="stylesheet" href="assets/fontawesome/css/all.min.css" />

    <!-- Formations Initiales Custom Styles -->
    <style>
      /* Formations Initiales Header Styles */
      .formations-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        margin-top: 10px;
        padding: 15px 0;
        border-bottom: 2px solid #e0e0e0;
      }

      .formations-selectors {
        display: flex;
        align-items: center;
        gap: 15px;
        order: 1;
      }

      .formations-title {
        color: #ffffff;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 1px;
        order: 2;
        background: #2c3e50;
        padding: 4px 8px;
        border-radius: 3px;
        border: 1px solid #34495e;
      }

      .specialty-placeholder {
        color: #6c757d;
        font-style: italic;
        font-size: 11px;
        min-width: 200px;
        display: inline-block;
      }

      .user-role {
        background: #3498db;
        color: white;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
      }
    </style>
  </head>
  <body>
    <!-- Loading Indicator Template (will be cloned by JS) -->
    <template id="loading-indicator-template">
      <div class="loading-container">
        <div class="loading-text">
          <span class="loading-letter">G</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">S</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">A</span>
        </div>
      </div>
    </template>

    <!-- Database Connection Error Screen -->
    <div class="db-error-screen" id="db-error-screen">
      <div class="db-error-content">
        <div class="db-error-icon">
          <i class="fas fa-database"></i>
          <i class="fas fa-times db-error-x"></i>
        </div>
        <h2>Veuillez vérifier la connexion à la base de données</h2>
        <p id="db-error-message">
          Impossible de se connecter au serveur de base de données.
        </p>
        <button id="retry-connection" class="action-btn">Réessayer</button>
      </div>
    </div>

    <div class="app-container" id="app-container">
      <!-- Floating lateral tabs -->
      <div class="lateral-tabs" id="lateral-tabs">
        <div class="tab active" data-tab="dashboard">
          <i class="fas fa-tachometer-alt"></i>
          <span class="tab-tooltip">Dashboard</span>
        </div>
        <div class="tab" data-tab="map">
          <i class="fas fa-map-marked-alt"></i>
          <span class="tab-tooltip">Map</span>
        </div>
        <div class="tab" data-tab="list">
          <i class="fas fa-list-ul"></i>
          <span class="tab-tooltip">List</span>
        </div>
        <div class="tab" data-tab="actions">
          <i class="fas fa-tasks"></i>
          <span class="tab-tooltip">Actions</span>
        </div>
        <div class="tab" data-tab="formations">
          <i class="fas fa-graduation-cap"></i>
          <span class="tab-tooltip">Formations initiales</span>
        </div>
        <!-- Toggle button for collapsing/expanding tabs -->
        <div class="tabs-toggle" id="tabs-toggle">
          <i class="fas fa-chevron-left"></i>
        </div>
      </div>

      <!-- Content area -->
      <div class="content-area">
        <!-- Dashboard Tab Content -->
        <div class="tab-content active" id="dashboard-content">
          <h1>Dashboard</h1>

          <!-- iPhone-style Toggle Buttons Row -->
          <div class="dashboard-toggle-container">
            <div class="dashboard-toggle-buttons">
              <div class="toggle-slider"></div>
              <button class="dashboard-toggle-btn active" data-view="planning">Planning</button>
              <button class="dashboard-toggle-btn" data-view="timeline">KPIs • Formations</button>
              <button class="dashboard-toggle-btn" data-view="analytics">KPIs • Stagiaires</button>
              <button class="dashboard-toggle-btn" data-view="overview">Overview</button>
              <button class="dashboard-toggle-btn" data-view="reports">Recommendations</button>
            </div>
          </div>

          <!-- Main Dashboard View (Planning + Timeline) -->
          <div class="dashboard-main-view" id="dashboard-main-view">
            <!-- Planning Schedule - Horizontal Scrollable Calendar -->
          <div class="planning-schedule-container">
            <!-- Control buttons container -->
            <div class="timeline-controls">
              <!-- Left section: Current Year Display -->
              <div class="timeline-left">
                <div id="current-year-display" class="current-year-display">2025</div>
              </div>

              <!-- Center section: Drag indicator -->
              <div class="timeline-center">
                <div class="drag-indicator">
                  <i class="fas fa-arrow-left"></i>
                  <i class="fas fa-hand-pointer"></i>
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>

              <!-- Right section: Buttons -->
              <div class="timeline-right">
                <button id="refresh-btn" class="timeline-btn refresh-btn" title="Rafraîchir les données">
                  <i class="fas fa-sync-alt"></i>
                </button>
                <button id="current-week-btn" class="timeline-btn current-week-btn" title="Aller à la semaine actuelle">
                  <i class="fas fa-calendar-day"></i>
                </button>
              </div>
            </div>

            <div class="planning-schedule" id="planning-schedule">
              <!-- Week columns will be generated by JavaScript -->
            </div>
            <!-- Fixed training count display -->
            <div id="active-trainings-count" class="active-trainings-count">00 formations en cours</div>
          </div>

          <!-- Activities Timeline - Horizontal Timeline -->
          <div class="activities-timeline-container">
            <div class="activities-timeline" id="activities-timeline">
              <!-- Activities will be generated by JavaScript -->
            </div>
          </div>

          </div>

          <!-- KPI Dashboard View (Statistics) -->
          <div class="dashboard-kpi-view" id="dashboard-kpi-view">
            <div class="dashboard-borderpane">
              <div class="kpi-container">
                <div class="loading-message">Chargement des statistiques...</div>
                <div class="kpi-row" style="display: none;">
                  <!-- Full page training statistics with domain selection -->
                  <div class="kpi-training-fullpage">
                    <!-- Domain selection circles - larger and including "Tout" -->
                    <div class="domain-selection-container" id="formations-domain-selection-container">
                      <div class="domain-circle active" data-domain="tout">
                        <span>Tout</span>
                      </div>
                      <!-- Domain circles will be populated dynamically -->
                    </div>

                    <!-- Training statistics display -->
                    <div class="training-stats-container">
                      <div class="training-stats-left">
                        <!-- Evolution chart -->
                        <div class="evolution-section">
                          <div class="evolution-title">Évolution des formations</div>
                          <div class="evolution-chart-container">
                            <canvas id="training-evolution-chart"></canvas>
                          </div>
                          <div class="evolution-percentage" id="evolution-percentage">0%</div>
                          <div class="evolution-label">Évolution annuelle</div>

                          <!-- Evolution details -->
                          <div class="evolution-details">
                            <div class="evolution-details-title">Analyse de tendance</div>
                            <div class="evolution-details-text" id="evolution-details-text">
                              Cette métrique montre l'évolution du nombre de formations par rapport à l'année précédente.
                              Une tendance positive indique une augmentation de l'activité de formation.
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="training-stats-right">
                        <!-- Total count and breakdown -->
                        <div class="training-count-section">
                          <!-- Top section with count and formations side by side -->
                          <div class="training-count-header">
                            <div class="training-count-left">
                              <div class="training-total-count" id="training-total-count">0</div>
                              <div class="training-total-label" id="training-total-label">Formations</div>
                            </div>

                            <div class="training-count-right">
                              <div class="formations-list-title clickable-title" id="formations-list-title">Liste des formations</div>
                              <div class="formations-list" id="formations-list">
                                <!-- Will be populated dynamically -->
                              </div>
                            </div>
                          </div>

                          <!-- Training summary -->
                          <div class="training-summary">
                            <div class="training-summary-title">Résumé des formations</div>
                            <div class="training-summary-text" id="training-summary-text">
                              Nombre total de formations disponibles dans le domaine sélectionné.
                            </div>
                          </div>

                          <!-- Training types breakdown -->
                          <div class="training-types-breakdown" id="training-types-breakdown">
                            <!-- Will be populated dynamically -->
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="error-message" style="display: none;">Erreur lors du chargement des statistiques</div>
              </div>
            </div>
          </div>

          <!-- Trainee KPI Dashboard View (Statistics) -->
          <div class="dashboard-trainee-kpi-view" id="dashboard-trainee-kpi-view">
            <div class="dashboard-borderpane">
              <div class="kpi-container">
                <div class="loading-message">Chargement des statistiques des stagiaires...</div>
                <div class="kpi-row" style="display: none;">
                  <!-- Full page trainee statistics with domain selection -->
                  <div class="kpi-training-fullpage">
                    <!-- Domain selection circles - larger and including "Tout" -->
                    <div class="domain-selection-container" id="trainee-domain-selection-container">
                      <div class="domain-circle active" data-domain="tout">
                        <span>Tout</span>
                      </div>
                      <!-- Domain circles will be populated dynamically -->
                    </div>

                    <!-- Trainee statistics display -->
                    <div class="training-stats-container">
                      <div class="training-stats-left">
                        <!-- Evolution chart -->
                        <div class="evolution-section">
                          <div class="evolution-title">Évolution des stagiaires</div>
                          <div class="evolution-chart-container">
                            <canvas id="trainee-evolution-chart"></canvas>
                          </div>
                          <div class="evolution-percentage" id="trainee-evolution-percentage">0%</div>
                          <div class="evolution-label">Évolution annuelle</div>
                          <div class="evolution-details" id="trainee-evolution-details-text">Chargement des données...</div>
                        </div>
                      </div>

                      <div class="training-stats-right">
                        <!-- Total count and breakdown -->
                        <div class="training-count-section">
                          <!-- Top section with count and types side by side -->
                          <div class="training-count-header">
                            <div class="training-count-left">
                              <div class="training-total-count" id="trainee-total-count">0</div>
                              <div class="training-total-label" id="trainee-total-label">Stagiaires</div>
                            </div>
                            <div class="training-count-right">
                              <!-- Top 3 unités -->
                              <div class="top-unites-section" id="top-unites-section">
                                <div class="top-unites-title">Top 3 Unités</div>
                                <div class="top-unites-list" id="top-unites-list">
                                  <!-- Will be populated dynamically -->
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Training summary -->
                          <div class="training-summary">
                            <div class="training-summary-title">Résumé des stagiaires</div>
                            <div class="training-summary-text" id="trainee-summary-text">
                              Nombre total de stagiaires dans le domaine sélectionné.
                            </div>
                          </div>

                          <!-- Training types breakdown -->
                          <div class="training-types-breakdown" id="trainee-types-breakdown">
                            <div class="training-types-title">Répartition par type</div>
                            <div class="training-types-list" id="trainee-types-list">
                              <!-- Will be populated dynamically -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="error-message" style="display: none;">Erreur lors du chargement des statistiques des stagiaires</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Map Tab Content -->
        <div class="tab-content" id="map-content">
          <h1>Map View</h1>
          <div class="map-container">
            <div id="morocco-map"></div>

            <!-- Mission Info Display (Icon + Text) -->
            <div id="mission-info-display" class="mission-info-display" style="display: none;">
              <div class="mission-info-icon">
                <i class="fas fa-crosshairs"></i>
              </div>
              <div class="mission-info-text">
                <div id="mission-info-date" class="mission-info-card">-</div>
                <div id="mission-info-type" class="mission-info-card">-</div>
                <hr class="mission-info-divider">
                <div id="mission-info-mission" class="mission-info-line">-</div>
                <div id="mission-info-route" class="mission-info-line">-</div>
                <div id="mission-info-ville" class="mission-info-line" style="display: none;">-</div>
                <div id="mission-info-bie" class="mission-info-line" style="display: none;">-</div>
                <div id="mission-info-status" class="mission-info-line">-</div>
              </div>
            </div>

            <!-- Tabbed Mission Info Display (for multiple current missions) -->
            <div id="tabbed-mission-info-display" class="tabbed-mission-info-display" style="display: none;">
              <div class="mission-tabs-header">
                <div id="mission-tabs" class="mission-tabs">
                  <!-- Tabs will be generated dynamically -->
                </div>
              </div>
              <div class="mission-tabs-content">
                <div id="mission-tab-content" class="mission-tab-content">
                  <div class="mission-info-icon">
                    <i class="fas fa-crosshairs"></i>
                  </div>
                  <div class="mission-info-text">
                    <div id="tab-mission-info-date" class="mission-info-card">-</div>
                    <div id="tab-mission-info-type" class="mission-info-card">-</div>
                    <hr class="mission-info-divider">
                    <div id="tab-mission-info-mission" class="mission-info-line">-</div>
                    <div id="tab-mission-info-route" class="mission-info-line">-</div>
                    <div id="tab-mission-info-ville" class="mission-info-line" style="display: none;">-</div>
                    <div id="tab-mission-info-bie" class="mission-info-line" style="display: none;">-</div>
                    <div id="tab-mission-info-status" class="mission-info-line">-</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- GSA Logo Overlay (shown when no toggle is active) -->
            <div class="map-logo-overlay" id="map-logo-overlay">
              <img src="gsa.png" alt="GSA Logo">
            </div>

            <!-- Floating toggle buttons for map data layers -->
            <div class="map-toggle-container">
              <button class="map-toggle-btn" data-layer="missiles">
                <i class="fas fa-rocket"></i>
                <span>Missiles</span>
              </button>
              <button class="map-toggle-btn" data-layer="drones">
                <i class="fas fa-plane"></i>
                <span>Drones</span>
              </button>
              <button class="map-toggle-btn" data-layer="personnel">
                <i class="fas fa-user-shield"></i>
                <span>Personnel drone</span>
              </button>
              <button class="map-toggle-btn" data-layer="stats">
                <i class="fas fa-chart-bar"></i>
                <span>Statistiques</span>
              </button>
            </div>

            <!-- Missions Panel (shown only when missiles toggle is active) -->
            <div class="missions-panel" id="missions-panel" style="display: none;">
              <div class="missions-header">
                <h3>Prochaines interventions</h3>
              </div>
              <div class="missions-list" id="missions-list">
                <!-- Mission items will be populated here -->
              </div>
            </div>
          </div>
        </div>

        <!-- List Tab Content -->
        <div class="tab-content" id="list-content">
          <h1>Formations</h1>
          <div class="split-container">
            <!-- Panneau de gauche (liste des formations) -->
            <div class="split-panel left-panel" id="trainings-list-panel">
              <div class="panel-header">
                <div class="domain-filter" id="domain-filter">
                  <!-- La liste des domaines sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des domaines...</div>
                </div>
              </div>
              <div class="panel-content">
                <div class="trainings-list" id="trainings-list">
                  <!-- La liste des formations sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des formations...</div>
                </div>
              </div>
            </div>

            <!-- Séparateur vertical redimensionnable -->
            <div class="split-resizer vertical-resizer" id="vertical-resizer"></div>

            <!-- Panneau de droite (vue unique) -->
            <div class="split-panel right-panel" id="details-panel">
              <div class="panel-header">
                <h2 id="training-header">Veuillez sélectionner une formation</h2>
              </div>
              <div class="panel-content">
                <div class="training-details" id="training-details">
                  <div class="placeholder-message">Veuillez sélectionner une formation</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions Tab Content -->
        <div class="tab-content" id="actions-content">
          <h1>Actions List</h1>
          <div class="actions-container">
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-file-export"></i></div>
              <div class="action-details">
                <h3>Export Data</h3>
                <p>Export data to various formats</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-sync"></i></div>
              <div class="action-details">
                <h3>Refresh Data</h3>
                <p>Update all data sources</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-cog"></i></div>
              <div class="action-details">
                <h3>Settings</h3>
                <p>Configure application settings</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-user-cog"></i></div>
              <div class="action-details">
                <h3>User Management</h3>
                <p>Manage user accounts and permissions</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
          </div>
        </div>

        <!-- Formations Initiales Tab Content -->
        <div class="tab-content" id="formations-content">
          <div class="formations-header">
            <div class="formations-selectors">
              <span class="user-role">Admin</span>
              <select class="formation-selector" id="formation-selector">
                <option value="">Sélectionner une formation...</option>
                <!-- Options chargées dynamiquement depuis la base de données -->
              </select>
              <select class="specialty-selector" id="specialty-selector" disabled>
                <option value="">Sélectionner une spécialité...</option>
              </select>
            </div>
            <h1 class="formations-title">formations initiales</h1>
          </div>

          <!-- Navigation latérale Formations -->
          <div class="formations-layout" id="formations-layout" style="display: none;">
            <div class="formations-sidebar">
              <div class="formations-logo">
                <i class="fas fa-graduation-cap"></i>
                <span id="formation-name">Formation</span>
              </div>
              <nav class="formations-nav">
                <div class="formations-nav-item active" data-section="dashboard">
                  <i class="fas fa-tachometer-alt"></i>
                  <span>Tableau de bord</span>
                </div>
                <div class="formations-nav-item" data-section="stagiaires">
                  <i class="fas fa-users"></i>
                  <span>Stagiaires</span>
                </div>
                <div class="formations-nav-item" data-section="fonctions">
                  <i class="fas fa-tasks"></i>
                  <span>Fonctions</span>
                </div>
                <div class="formations-nav-item" data-section="competences">
                  <i class="fas fa-bullseye"></i>
                  <span>Compétences</span>
                </div>
                <div class="formations-nav-item" data-section="modules">
                  <i class="fas fa-book"></i>
                  <span>Modules & Cours</span>
                </div>
                <div class="formations-nav-item" data-section="progression">
                  <i class="fas fa-calendar-week"></i>
                  <span>Progression hebdo</span>
                </div>
                <div class="formations-nav-item" data-section="evaluations">
                  <i class="fas fa-clipboard-check"></i>
                  <span>Évaluations</span>
                </div>
                <div class="formations-nav-item" data-section="rapports">
                  <i class="fas fa-chart-bar"></i>
                  <span>Rapports / Export</span>
                </div>
                <div class="formations-nav-item" data-section="parametres">
                  <i class="fas fa-cog"></i>
                  <span>Paramètres</span>
                </div>
              </nav>
            </div>

            <!-- Message de sélection -->
            <div class="formations-selection-message" id="formations-selection-message">
              <div class="selection-content">
                <i class="fas fa-graduation-cap"></i>
                <h2>Formations Initiales</h2>
                <p>Veuillez sélectionner une formation pour commencer.</p>
                <div class="formations-hierarchy">
                  <div class="hierarchy-level">
                    <strong>Formation</strong>
                    <span>SPU, Alpha, Beta...</span>
                  </div>
                  <div class="hierarchy-arrow">↓</div>
                  <div class="hierarchy-level">
                    <strong>Spécialité</strong>
                    <span>GNSS, Drones, Soutien...</span>
                  </div>
                  <div class="hierarchy-split">
                    <div class="hierarchy-branch">
                      <div class="hierarchy-arrow">↓</div>
                      <div class="hierarchy-level">
                        <strong>Fonction</strong>
                        <span>F1, F2, F3...</span>
                      </div>
                      <div class="hierarchy-arrow">↓</div>
                      <div class="hierarchy-level">
                        <strong>Compétence</strong>
                        <span>C1, C2, C3...</span>
                      </div>
                    </div>
                    <div class="hierarchy-branch">
                      <div class="hierarchy-arrow">↓</div>
                      <div class="hierarchy-level">
                        <strong>Module</strong>
                        <span>GPS Base, Navigation...</span>
                      </div>
                      <div class="hierarchy-arrow">↓</div>
                      <div class="hierarchy-level">
                        <strong>Cours</strong>
                        <span>Introduction, Pratique...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Contenu principal Formations -->
            <div class="formations-main-content">
              <!-- Dashboard Section -->
              <div class="formations-section active" id="formations-dashboard">
                <div class="formations-kpis">
                  <div class="formations-kpi-card">
                    <div class="formations-kpi-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <div class="formations-kpi-content">
                      <div class="formations-kpi-value" id="kpi-stagiaires">-</div>
                      <div class="formations-kpi-label">Stagiaires actifs</div>
                    </div>
                  </div>
                  <div class="formations-kpi-card">
                    <div class="formations-kpi-icon">
                      <i class="fas fa-tasks"></i>
                    </div>
                    <div class="formations-kpi-content">
                      <div class="formations-kpi-value" id="kpi-fonctions">-</div>
                      <div class="formations-kpi-label">Fonctions</div>
                    </div>
                  </div>
                  <div class="formations-kpi-card">
                    <div class="formations-kpi-icon">
                      <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="formations-kpi-content">
                      <div class="formations-kpi-value" id="kpi-competences">-</div>
                      <div class="formations-kpi-label">Compétences</div>
                    </div>
                  </div>
                  <div class="formations-kpi-card">
                    <div class="formations-kpi-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="formations-kpi-content">
                      <div class="formations-kpi-value" id="kpi-heures">-</div>
                      <div class="formations-kpi-label">Heures cette semaine</div>
                    </div>
                  </div>
                </div>

                <div class="spu-dashboard-widgets">
                  <div class="spu-widget">
                    <div class="spu-widget-header">
                      <h3>Planning hebdomadaire</h3>
                      <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="spu-widget-content">
                      <div class="spu-planning-grid">
                        <div class="spu-planning-header">
                          <div class="spu-time-slot">Heure</div>
                          <div class="spu-day">Lundi</div>
                          <div class="spu-day">Mardi</div>
                          <div class="spu-day">Mercredi</div>
                          <div class="spu-day">Jeudi</div>
                          <div class="spu-day">Vendredi</div>
                        </div>
                        <div class="spu-planning-row">
                          <div class="spu-time-slot">08h-10h</div>
                          <div class="spu-course-slot">GPS Niveau 1</div>
                          <div class="spu-course-slot">Cartographie</div>
                          <div class="spu-course-slot">Navigation</div>
                          <div class="spu-course-slot">Théorie Drone</div>
                          <div class="spu-course-slot">Pratique GPS</div>
                        </div>
                        <div class="spu-planning-row">
                          <div class="spu-time-slot">10h-12h</div>
                          <div class="spu-course-slot">Pratique GPS</div>
                          <div class="spu-course-slot">Drone Pilotage</div>
                          <div class="spu-course-slot">Maintenance</div>
                          <div class="spu-course-slot">Évaluation</div>
                          <div class="spu-course-slot">Révisions</div>
                        </div>
                        <div class="spu-planning-row">
                          <div class="spu-time-slot">13h-15h</div>
                          <div class="spu-course-slot">Théorie Avancée</div>
                          <div class="spu-course-slot">Simulation</div>
                          <div class="spu-course-slot">Projet</div>
                          <div class="spu-course-slot">Soutien</div>
                          <div class="spu-course-slot">Bilan</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="spu-widget">
                    <div class="spu-widget-header">
                      <h3>Avancement par compétence</h3>
                      <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="spu-widget-content">
                      <div class="spu-competence-progress">
                        <div class="spu-progress-item">
                          <span class="spu-competence-name">C1 - Navigation GPS</span>
                          <div class="spu-progress-bar">
                            <div class="spu-progress-fill" style="width: 83%"></div>
                          </div>
                          <span class="spu-progress-value">83%</span>
                        </div>
                        <div class="spu-progress-item">
                          <span class="spu-competence-name">C2 - Cartographie</span>
                          <div class="spu-progress-bar">
                            <div class="spu-progress-fill" style="width: 67%"></div>
                          </div>
                          <span class="spu-progress-value">67%</span>
                        </div>
                        <div class="spu-progress-item">
                          <span class="spu-competence-name">C3 - Pilotage Drone</span>
                          <div class="spu-progress-bar">
                            <div class="spu-progress-fill" style="width: 45%"></div>
                          </div>
                          <span class="spu-progress-value">45%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="spu-widget">
                    <div class="spu-widget-header">
                      <h3>Stagiaires en retard</h3>
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="spu-widget-content">
                      <div class="spu-alert-list">
                        <div class="spu-alert-item">
                          <span class="spu-student-name">Ahmed BENALI</span>
                          <span class="spu-alert-reason">C1 < 50%</span>
                          <span class="spu-alert-status danger">Critique</span>
                        </div>
                        <div class="spu-alert-item">
                          <span class="spu-student-name">Fatima ALAOUI</span>
                          <span class="spu-alert-reason">Absence cours C2</span>
                          <span class="spu-alert-status warning">Attention</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Stagiaires Section -->
              <div class="formations-section" id="formations-stagiaires">
                <div class="formations-section-header">
                  <h2>Gestion des Stagiaires</h2>
                  <button class="formations-btn formations-btn-primary" id="add-stagiaire-btn">
                    <i class="fas fa-plus"></i> Ajouter un stagiaire
                  </button>
                </div>
                <div class="formations-filters">
                  <input type="text" class="formations-search" placeholder="Rechercher un stagiaire...">
                  <select class="formations-filter">
                    <option value="">Toutes les spécialités</option>
                    <option value="gnss">GNSS</option>
                    <option value="drones">Drones</option>
                    <option value="soutien">Soutien</option>
                  </select>
                  <select class="formations-filter">
                    <option value="">Tous les statuts</option>
                    <option value="en-cours">En cours</option>
                    <option value="retard">En retard</option>
                    <option value="termine">Terminé</option>
                  </select>
                </div>
                <div class="formations-table-container">
                  <table class="formations-table">
                    <thead>
                      <tr>
                        <th>Nom</th>
                        <th>Prénom</th>
                        <th>Spécialité</th>
                        <th>Progression globale</th>
                        <th>Statut</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="stagiaires-table-body">
                      <tr>
                        <td>BENALI</td>
                        <td>Ahmed</td>
                        <td>GNSS</td>
                        <td>
                          <div class="formations-progress-bar small">
                            <div class="formations-progress-fill" style="width: 45%"></div>
                          </div>
                          <span class="formations-progress-text">45%</span>
                        </td>
                        <td><span class="formations-status danger">En retard</span></td>
                        <td>
                          <button class="formations-btn formations-btn-small" onclick="viewStagiaire(1)">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="formations-btn formations-btn-small" onclick="editStagiaire(1)">
                            <i class="fas fa-edit"></i>
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>ALAOUI</td>
                        <td>Fatima</td>
                        <td>Drones</td>
                        <td>
                          <div class="formations-progress-bar small">
                            <div class="formations-progress-fill" style="width: 78%"></div>
                          </div>
                          <span class="formations-progress-text">78%</span>
                        </td>
                        <td><span class="formations-status success">En cours</span></td>
                        <td>
                          <button class="formations-btn formations-btn-small" onclick="viewStagiaire(2)">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="formations-btn formations-btn-small" onclick="editStagiaire(2)">
                            <i class="fas fa-edit"></i>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Fonctions Section -->
              <div class="formations-section" id="formations-fonctions">
                <div class="formations-section-header">
                  <h2>Gestion des Fonctions</h2>
                  <button class="formations-btn formations-btn-primary" id="add-fonction-btn">
                    <i class="fas fa-plus"></i> Ajouter une fonction
                  </button>
                </div>
                <div class="formations-hierarchy-display">
                  <div class="hierarchy-breadcrumb">
                    <span class="breadcrumb-item" id="breadcrumb-formation">Formation</span>
                    <i class="fas fa-chevron-right"></i>
                    <span class="breadcrumb-item" id="breadcrumb-specialite">Spécialité</span>
                    <i class="fas fa-chevron-right"></i>
                    <span class="breadcrumb-item active">Fonctions</span>
                  </div>
                </div>
                <div class="formations-fonctions-grid" id="fonctions-grid">
                  <!-- Les fonctions seront chargées dynamiquement -->
                </div>
              </div>

              <!-- Compétences Section -->
              <div class="formations-section" id="formations-competences">
                <div class="formations-section-header">
                  <h2>Gestion des Compétences</h2>
                  <button class="formations-btn formations-btn-primary" id="add-competence-btn">
                    <i class="fas fa-plus"></i> Ajouter une compétence
                  </button>
                </div>
                <div class="formations-competences-grid">
                  <div class="formations-competence-card">
                    <div class="formations-competence-header">
                      <h3>F1.C1 - Navigation de base</h3>
                      <span class="formations-competence-code">F1.C1</span>
                    </div>
                    <div class="formations-competence-description">
                      Utilisation des fonctions GPS de base pour la navigation
                    </div>
                    <div class="formations-competence-stats">
                      <div class="formations-stat">
                        <span class="formations-stat-value">5</span>
                        <span class="formations-stat-label">Cours associés</span>
                      </div>
                      <div class="formations-stat">
                        <span class="formations-stat-value">83%</span>
                        <span class="formations-stat-label">Progression moyenne</span>
                      </div>
                    </div>
                    <div class="formations-competence-actions">
                      <button class="formations-btn formations-btn-small">Modifier</button>
                      <button class="formations-btn formations-btn-small">Voir détails</button>
                    </div>
                  </div>
                  <div class="formations-competence-card">
                    <div class="formations-competence-header">
                      <h3>F1.C2 - Lecture de cartes</h3>
                      <span class="formations-competence-code">F1.C2</span>
                    </div>
                    <div class="formations-competence-description">
                      Interprétation des cartes topographiques et navigation
                    </div>
                    <div class="formations-competence-stats">
                      <div class="formations-stat">
                        <span class="formations-stat-value">4</span>
                        <span class="formations-stat-label">Cours associés</span>
                      </div>
                      <div class="formations-stat">
                        <span class="formations-stat-value">67%</span>
                        <span class="formations-stat-label">Progression moyenne</span>
                      </div>
                    </div>
                    <div class="formations-competence-actions">
                      <button class="formations-btn formations-btn-small">Modifier</button>
                      <button class="formations-btn formations-btn-small">Voir détails</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Modules & Cours Section -->
              <div class="formations-section" id="formations-modules">
                <div class="formations-section-header">
                  <h2>Modules & Cours</h2>
                  <div class="formations-section-actions">
                    <button class="formations-btn formations-btn-primary" id="add-module-btn">
                      <i class="fas fa-plus"></i> Ajouter un module
                    </button>
                    <button class="formations-btn formations-btn-primary" id="add-cours-btn">
                      <i class="fas fa-plus"></i> Ajouter un cours
                    </button>
                  </div>
                </div>
                <div class="formations-hierarchy-display">
                  <div class="hierarchy-breadcrumb">
                    <span class="breadcrumb-item" id="breadcrumb-formation-modules">Formation</span>
                    <i class="fas fa-chevron-right"></i>
                    <span class="breadcrumb-item" id="breadcrumb-specialite-modules">Spécialité</span>
                    <i class="fas fa-chevron-right"></i>
                    <span class="breadcrumb-item active">Modules & Cours</span>
                  </div>
                  <div class="formations-info-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Les modules sont organisés au niveau de la spécialité et partagés entre toutes les fonctions.</span>
                  </div>
                </div>
                <div class="formations-modules-layout">
                  <div class="formations-modules-sidebar">
                    <h3>Modules de la spécialité</h3>
                    <div class="formations-module-list" id="modules-list">
                      <!-- Les modules seront chargés dynamiquement selon la spécialité sélectionnée -->
                    </div>
                  </div>
                  <div class="spu-courses-content">
                    <div class="spu-courses-header">
                      <h3>Cours du Module GPS</h3>
                      <div class="spu-courses-filters">
                        <input type="text" class="spu-search" placeholder="Rechercher un cours...">
                        <select class="spu-filter">
                          <option value="">Toutes les compétences</option>
                          <option value="c1">C1 - Navigation GPS</option>
                          <option value="c2">C2 - Cartographie</option>
                        </select>
                      </div>
                    </div>
                    <div class="spu-table-container">
                      <table class="spu-table">
                        <thead>
                          <tr>
                            <th>Titre</th>
                            <th>Volume horaire</th>
                            <th>Compétences associées</th>
                            <th>Statut</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>GPS Niveau 1</td>
                            <td>4h</td>
                            <td>
                              <span class="spu-competence-tag">C1 (25%)</span>
                              <span class="spu-competence-tag">C2 (25%)</span>
                            </td>
                            <td><span class="spu-status success">Actif</span></td>
                            <td>
                              <button class="spu-btn spu-btn-small">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="spu-btn spu-btn-small">
                                <i class="fas fa-trash"></i>
                              </button>
                            </td>
                          </tr>
                          <tr>
                            <td>Pratique GPS Avancée</td>
                            <td>6h</td>
                            <td>
                              <span class="spu-competence-tag">C1 (50%)</span>
                            </td>
                            <td><span class="spu-status success">Actif</span></td>
                            <td>
                              <button class="spu-btn spu-btn-small">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="spu-btn spu-btn-small">
                                <i class="fas fa-trash"></i>
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Progression Hebdomadaire Section -->
              <div class="formations-section" id="formations-progression">
                <div class="formations-section-header">
                  <h2>Progression Hebdomadaire</h2>
                  <div class="formations-week-navigation">
                    <button class="formations-btn formations-btn-small" id="prev-week">
                      <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="formations-current-week">Semaine du 18-22 Septembre 2025</span>
                    <button class="formations-btn formations-btn-small" id="next-week">
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </div>
                </div>
                <div class="formations-planning-container">
                  <div class="formations-planning-grid large">
                    <div class="formations-planning-header">
                      <div class="formations-time-slot">Heure</div>
                      <div class="formations-day">Lundi<br><small>18 Sept</small></div>
                      <div class="formations-day">Mardi<br><small>19 Sept</small></div>
                      <div class="formations-day">Mercredi<br><small>20 Sept</small></div>
                      <div class="formations-day">Jeudi<br><small>21 Sept</small></div>
                      <div class="formations-day">Vendredi<br><small>22 Sept</small></div>
                    </div>
                    <div class="formations-planning-row">
                      <div class="formations-time-slot">08h-10h</div>
                      <div class="formations-course-slot detailed" onclick="showCourseDetails('gps1')">
                        <div class="formations-course-title">GPS Niveau 1</div>
                        <div class="formations-course-info">F1.C1, F1.C2 • Salle A</div>
                      </div>
                      <div class="formations-course-slot detailed" onclick="showCourseDetails('carto1')">
                        <div class="formations-course-title">Cartographie</div>
                        <div class="formations-course-info">F1.C2 • Salle B</div>
                      </div>
                      <div class="formations-course-slot detailed" onclick="showCourseDetails('nav1')">
                        <div class="formations-course-title">Navigation</div>
                        <div class="formations-course-info">F1.C1 • Terrain</div>
                      </div>
                      <div class="formations-course-slot detailed" onclick="showCourseDetails('drone1')">
                        <div class="formations-course-title">Théorie Drone</div>
                        <div class="formations-course-info">F1.C3 • Salle C</div>
                      </div>
                      <div class="formations-course-slot detailed" onclick="showCourseDetails('prat1')">
                        <div class="formations-course-title">Pratique GPS</div>
                        <div class="formations-course-info">F1.C1 • Terrain</div>
                      </div>
                    </div>
                    <div class="formations-planning-row">
                      <div class="formations-time-slot">10h-12h</div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Pratique GPS</div>
                        <div class="formations-course-info">F1.C1 • Terrain</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Drone Pilotage</div>
                        <div class="formations-course-info">F1.C1 • Terrain</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Maintenance</div>
                        <div class="formations-course-info">F2.C1 • Atelier</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Évaluation</div>
                        <div class="formations-course-info">F1.C1, F1.C2 • Salle A</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Révisions</div>
                        <div class="formations-course-info">Toutes • Salle B</div>
                      </div>
                    </div>
                    <div class="formations-planning-row pause">
                      <div class="formations-time-slot">12h-13h</div>
                      <div class="formations-course-slot pause-slot" colspan="5">PAUSE DÉJEUNER</div>
                      <div class="formations-course-slot pause-slot"></div>
                      <div class="formations-course-slot pause-slot"></div>
                      <div class="formations-course-slot pause-slot"></div>
                      <div class="formations-course-slot pause-slot"></div>
                    </div>
                    <div class="formations-planning-row">
                      <div class="formations-time-slot">13h-15h</div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Théorie Avancée</div>
                        <div class="formations-course-info">F2.C3 • Salle A</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Simulation</div>
                        <div class="formations-course-info">F1.C3 • Lab Info</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Projet</div>
                        <div class="formations-course-info">Toutes • Salle C</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Soutien</div>
                        <div class="formations-course-info">Individuel • Bureau</div>
                      </div>
                      <div class="formations-course-slot detailed">
                        <div class="formations-course-title">Bilan</div>
                        <div class="formations-course-info">Groupe • Salle A</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Autres sections (placeholder pour l'instant) -->
              <div class="formations-section" id="formations-evaluations">
                <h2>Évaluations</h2>
                <p>Section évaluations en cours de développement...</p>
              </div>

              <div class="formations-section" id="formations-rapports">
                <h2>Rapports / Export</h2>
                <p>Section rapports en cours de développement...</p>
              </div>

              <div class="formations-section" id="formations-parametres">
                <h2>Paramètres</h2>
                <p>Section paramètres en cours de développement...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo and Brand Text -->
    <div class="app-footer">
      <div class="brand-container">
        <img src="gsa.png" alt="GSA Logo" class="brand-logo" />
        <span class="brand-separator">|</span>
        <span class="brand-text">Groupe de Soutien Artillerie</span>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input type="text" id="search-input" placeholder="Rechercher..." />
        <button id="search-button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- Date Time Display, Fullscreen and Theme Switcher -->
    <div class="bottom-controls">
      <div class="datetime-display" id="datetime-display">
        16 octobre 2025, 22h30
      </div>
      <div class="fullscreen-toggle" id="fullscreen-toggle">
        <i class="fas fa-expand"></i>
      </div>
      <div class="theme-switcher" id="theme-switcher">
        <i class="fas fa-sun"></i>
      </div>
    </div>

    <!-- Leaflet JS -->
    <script src="assets/leaflet/leaflet.js"></script>
    <!-- Chart.js -->
    <script src="assets/chart.js/chart.min.js"></script>
    <!-- Chart.js Datalabels Plugin -->
    <script src="https://unpkg.com/chartjs-plugin-datalabels@2"></script>
    <!-- Main Application Script -->
    <script src="renderer.js"></script>

    <!-- Dashboard Toggle Buttons Script -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard toggle buttons
        const toggleButtons = document.querySelectorAll('.dashboard-toggle-btn');
        const toggleSlider = document.querySelector('.dashboard-toggle-buttons .toggle-slider');

        if (toggleButtons.length > 0 && toggleSlider) {
          // Set initial slider position for the first (active) button
          updateSliderPosition(toggleButtons[0]);

          // Add click event listeners to all toggle buttons
          toggleButtons.forEach((button, index) => {
            button.addEventListener('click', function() {
              // Remove active class from all buttons
              toggleButtons.forEach(btn => btn.classList.remove('active'));

              // Add active class to clicked button
              this.classList.add('active');

              // Update slider position
              updateSliderPosition(this);

              // Handle view switching logic here
              handleViewSwitch(this.dataset.view);
            });
          });
        }

        function updateSliderPosition(activeButton) {
          const buttonRect = activeButton.getBoundingClientRect();
          const containerRect = activeButton.parentElement.getBoundingClientRect();
          const leftOffset = buttonRect.left - containerRect.left - 4; // Account for container padding
          const buttonWidth = buttonRect.width;

          toggleSlider.style.left = leftOffset + 'px';
          toggleSlider.style.width = buttonWidth + 'px';
        }

        function handleViewSwitch(view) {
          console.log('Switching to view:', view);

          const mainView = document.getElementById('dashboard-main-view');
          const kpiView = document.getElementById('dashboard-kpi-view');
          const traineeKpiView = document.getElementById('dashboard-trainee-kpi-view');

          // Hide all views first
          if (mainView) mainView.style.display = 'none';
          if (kpiView) kpiView.style.display = 'none';
          if (traineeKpiView) traineeKpiView.style.display = 'none';

          switch(view) {
            case 'planning':
              console.log('Showing planning view - two sections sharing height');
              if (mainView) {
                mainView.style.display = 'flex';
              }
              break;
            case 'timeline':
              console.log('Showing KPIs • Formations view');
              if (kpiView) {
                kpiView.style.display = 'flex';
                kpiView.style.flexDirection = 'column';
                kpiView.style.height = '100%';
                kpiView.style.marginTop = '40px';
                // Initialize formations statistics when view is shown
                window.initializeFormationsStatistics();
              }
              break;
            case 'analytics':
              console.log('Showing KPIs • Stagiaires view');
              if (traineeKpiView) {
                traineeKpiView.style.display = 'flex';
                traineeKpiView.style.flexDirection = 'column';
                traineeKpiView.style.height = '100%';
                traineeKpiView.style.marginTop = '40px';
                // Initialize trainee statistics when view is shown
                window.initializeTraineeStatistics();
              }
              break;
            case 'overview':
              console.log('Showing overview view');
              // Future implementation
              break;
            case 'reports':
              console.log('Showing recommendations view');
              // Future implementation
              break;
            default:
              console.log('Unknown view:', view);
              // Default to planning view
              if (mainView) {
                mainView.style.display = 'flex';
              }
          }
        }
      });
    </script>

    <!-- Direct Map Initialization Script -->
    <script>
      // This script directly initializes the map when the page loads
      document.addEventListener('DOMContentLoaded', function () {
        console.log('Direct map initialization script loaded');

        // Initialize map after a delay
        setTimeout(function () {
          console.log('Attempting direct map initialization');

          try {
            // Get the map element
            const mapElement = document.getElementById('morocco-map');
            if (!mapElement) {
              console.error('Map element not found in direct script');
              return;
            }

            console.log('Map element found in direct script:', mapElement);
            console.log(
              'Map element dimensions:',
              mapElement.offsetWidth,
              'x',
              mapElement.offsetHeight
            );

            // Force the map element to be visible
            mapElement.style.display = 'block';
            mapElement.style.height = '100%';
            mapElement.style.width = '100%';
            mapElement.style.position = 'relative';

            // Check if Leaflet is loaded
            if (typeof L === 'undefined') {
              console.error('Leaflet not loaded in direct script');
              return;
            }

            console.log('Leaflet is loaded in direct script');

            // Check if map is already initialized
            if (mapElement._leaflet_id) {
              console.log('Map already initialized in direct script');
              return;
            }

            // Define initial map center and zoom level
            const initialCenter = [28.5, -8.0]; // Further adjusted center to position Morocco even higher in the view
            const initialZoom = 6.0; // Initial zoom level set to 6 as requested

            // Define missile view constants (shared between missile view and reset button)
            const MISSILE_VIEW_CENTER = [32.4, -4.8372]; // Zagora area coordinates
            const MISSILE_VIEW_ZOOM = 7.0; // Missile view zoom level

            // Create the map
            console.log('Creating map in direct script...');
            var directMap = L.map('morocco-map', {
              center: initialCenter,
              zoom: initialZoom,
              minZoom: 5.0, // Minimum zoom level
              maxZoom: 10,
              zoomControl: true,
              attributionControl: false, // Hide attribution control
              zoomSnap: 0.10, // Allow finer zoom control
              zoomDelta: 0.5, // Smoother zoom steps
              wheelPxPerZoomLevel: 120 // More sensitive mouse wheel zooming
            });

            console.log('Map created in direct script');

            // Add map controls (reset button and zoom level display)
            addZoomLevelDisplay(directMap);

            // Load Morocco GeoJSON data
            console.log('Loading Morocco GeoJSON data...');

            // Define style for the GeoJSON - simple style for Morocco boundaries
            function style(feature) {
              return {
                fillColor: '#333333', // dodgerblue
                weight: 0.5,
                opacity: 1,
                color: 'white',
                fillOpacity: 0.6,
                smoothFactor: 1,
              };
            }

            // Simple feature handler - no interactions
            function onEachFeature(feature, layer) {
              // No interactions needed, just display the boundaries
            }

            // Create a variable to hold the GeoJSON layer
            var moroccoGeoJSON;

            // For testing error handling - set to true to simulate error
            var simulateError = false;

            // Fetch the GeoJSON file
            console.log('Fetching morocco.geojson file...');

            // Direct fetch of morocco.geojson as requested with cache-busting
            fetch('assets/data/morocco.geojson?v=' + new Date().getTime())
              .then(function (response) {
                console.log('GeoJSON fetch response status:', response.status);
                if (!response.ok) {
                  throw new Error(
                    'Network response was not ok: ' + response.status
                  );
                }
                console.log('Parsing GeoJSON response...');
                return response.json();
              })
              .then(function (data) {
                console.log(
                  'GeoJSON data loaded successfully, features:',
                  data.features ? data.features.length : 'none'
                );

                try {
                  // Add the GeoJSON layer to the map
                  console.log('Creating GeoJSON layer...');
                  moroccoGeoJSON = L.geoJSON(data, {
                    style: style,
                    onEachFeature: onEachFeature,
                  });

                  console.log('Adding GeoJSON layer to map...');
                  moroccoGeoJSON.addTo(directMap);

                  console.log('Fitting map to GeoJSON bounds...');
                  directMap.fitBounds(moroccoGeoJSON.getBounds());

                  console.log('Morocco GeoJSON added to map successfully');

                  // Legend removed as requested

                  // Buttons removed as requested

                  // Fetching and displaying drone deployments disabled as requested
                  console.log('Drone fetching and display disabled as requested');

                  // Set up map toggle buttons
                  setupMapToggleButtons();
                } catch (err) {
                  console.error('Error processing GeoJSON data:', err);
                  throw err;
                }
              })
              .catch(function (error) {
                console.error('Error loading GeoJSON:', error);

                // Display an error message in the map container
                console.log(
                  'Displaying error message for GeoJSON load failure'
                );

                // Create an error message element
                var errorDiv = document.createElement('div');
                errorDiv.className = 'map-error-message';
                errorDiv.innerHTML = `
                  <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
                  <h3>Erreur de chargement de la carte</h3>
                  <p>Impossible de charger les données cartographiques du Maroc.</p>
                  <p class="error-details">Détails: ${error.message}</p>
                  <p class="error-path">Chemin du fichier: assets/data/morocco.geojson</p>
                `;

                // Clear the map container and add the error message
                var mapContainer = document.getElementById('morocco-map');

                if (!mapContainer) {
                  console.error('Map container not found for error message');
                  return;
                }

                console.log('Map container found, adding error message');

                // Create a centered container for the error message
                var centerDiv = document.createElement('div');
                centerDiv.className = 'map-error-container';
                centerDiv.appendChild(errorDiv);

                // Add the error container to the map
                try {
                  mapContainer.innerHTML = '';
                  mapContainer.appendChild(centerDiv);

                  // Set background color
                  mapContainer.style.backgroundColor = 'var(--bg-secondary)';

                  console.log('Error message added to map container');
                } catch (err) {
                  console.error(
                    'Error adding error message to map container:',
                    err
                  );
                }
              });

            // Global variable to store the drone layer group
            let droneLayerGroup = null;

            // Function to fetch and display drone deployments
            async function fetchAndDisplayDroneDeployments(map) {
              console.log('Fetching drone deployments...');

              // Restore default map settings for drone view
              restoreDefaultMapSettings(map);

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch drone deployments from the database
                const response = await window.api.getDroneDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching drone deployments:', response.error);
                  alert('Error fetching drone data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Drone deployments fetched:', JSON.stringify(deployments));
                console.log(`Total drone records: ${deployments.length}`);

                // Remove existing drone layer group if it exists
                if (droneLayerGroup) {
                  map.removeLayer(droneLayerGroup);
                }

                // Create a new layer group for all drone markers
                droneLayerGroup = L.layerGroup().addTo(map);

                // Clear the drone markers array
                droneMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No drone deployments found in database');
                  alert('No drone deployments found in database');
                  return;
                }

                // Process each drone deployment
                for (let i = 0; i < deployments.length; i++) {
                  const drone = deployments[i];

                  console.log(`Processing drone ${i+1}/${deployments.length}: ID=${drone.id}, Type=${drone.type_drone}, Position=[${drone.lat}, ${drone.lng}]`);

                  // Validate drone data
                  if (!drone.lat || !drone.lng || !drone.type_drone) {
                    console.warn(`Skipping drone ${i+1} due to missing data:`, drone);
                    continue;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                  const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                  // Store the drone position for fitting bounds
                  droneMarkers.push([lat, lng]);

                  // Create marker for the drone position - simple grey circle
                  const droneMarker = L.circleMarker([lat, lng], {
                    radius: 5, // Small circle
                    fillColor: '#808080', // Grey for all markers
                    color: '#fff',
                    weight: 1, // Thin border
                    opacity: 1,
                    fillOpacity: 0.8
                  }).addTo(droneLayerGroup);

                  // Calculate optimal label position to avoid overlaps
                  const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                  // Get the calculated label position
                  const labelLat = labelPosition.lat;
                  const labelLng = labelPosition.lng;

                  // Create a label with the simplified information
                  const labelIcon = L.divIcon({
                    className: 'drone-label',
                    html: `<div class="drone-label-content">
                      <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                      <div class="drone-info">
                        ${drone.unite || 'N/A'}
                      </div>
                      <div class="drone-type-container">
                        <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                      </div>
                    </div>`,
                    iconSize: [220, 65], // Size for three lines with styling
                    iconAnchor: [0, 30]  // Adjusted anchor point
                  });

                  // Add the label marker at the offset position
                  const labelMarker = L.marker([labelLat, labelLng], {
                    icon: labelIcon,
                    zIndexOffset: 1000 // Ensure labels are above other markers
                  }).addTo(droneLayerGroup);

                  // Create a subtle line connecting the marker to the label
                  const connectorLine = L.polyline(
                    [
                      [lat, lng],
                      [labelLat, labelLng] // Use calculated offset position
                    ],
                    {
                      color: 'var(--text-secondary)', // Use theme color
                      weight: 0.8, // Very thin line
                      opacity: 0.5, // Subtle
                      dashArray: '2,4'
                    }
                  ).addTo(droneLayerGroup);

                  // Add popup with simplified drone details
                  droneMarker.bindPopup(`
                    <div class="drone-popup">
                      <h3>Drone #${drone.id}</h3>
                      <p>Type: ${drone.type_drone || 'N/A'}</p>
                      <p>Unité: ${drone.unite || 'N/A'}</p>
                      <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                    </div>
                  `);

                  console.log(`Added drone marker ${i+1} to map`);
                }

                console.log(`Displayed ${droneMarkers.length} drone markers on map`);

                // Automatic refresh disabled as requested
                console.log('Automatic refresh disabled as requested');
              } catch (error) {
                console.error('Error displaying drone deployments:', error);
                alert('Error displaying drone deployments: ' + error.message);
              }
            }

            // Variable to store the refresh interval
            let droneRefreshInterval = null;

            // Function to set up automatic refresh of drone positions
            function setupDroneRefresh(map) {
              // Clear any existing interval
              if (droneRefreshInterval) {
                clearInterval(droneRefreshInterval);
              }

              // Set up new interval to refresh drone positions every 30 seconds
              droneRefreshInterval = setInterval(async () => {
                console.log('Refreshing drone positions...');
                try {
                  // Fetch drone deployments from the database
                  const response = await window.api.getDroneDeployments();

                  if (!response.success) {
                    console.error('Error refreshing drone deployments:', response.error);
                    return;
                  }

                  const deployments = response.data;

                  // Remove existing drone layer group
                  if (droneLayerGroup) {
                    map.removeLayer(droneLayerGroup);
                  }

                  // Create a new layer group for all drone markers
                  droneLayerGroup = L.layerGroup().addTo(map);

                  // Clear the drone markers array
                  droneMarkers = [];

                  // Check if we have any deployments
                  if (!deployments || deployments.length === 0) {
                    console.warn('No drone deployments found in database during refresh');
                    return;
                  }

                  // Process each drone deployment
                  for (let i = 0; i < deployments.length; i++) {
                    const drone = deployments[i];

                    // Validate drone data
                    if (!drone.lat || !drone.lng || !drone.type_drone) {
                      console.warn(`Skipping drone ${i+1} during refresh due to missing data:`, drone);
                      continue;
                    }

                    // Convert lat/lng to numbers if they're strings
                    const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                    const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                    // Store the drone position for fitting bounds
                    droneMarkers.push([lat, lng]);

                    // Create marker for the drone position - simple grey circle
                    const droneMarker = L.circleMarker([lat, lng], {
                      radius: 5, // Small circle
                      fillColor: '#808080', // Grey for all markers
                      color: '#fff',
                      weight: 1, // Thin border
                      opacity: 1,
                      fillOpacity: 0.8
                    }).addTo(droneLayerGroup);

                    // Calculate optimal label position to avoid overlaps
                    const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                    // Get the calculated label position
                    const labelLat = labelPosition.lat;
                    const labelLng = labelPosition.lng;

                    // Create a label with the simplified information
                    const labelIcon = L.divIcon({
                      className: 'drone-label',
                      html: `<div class="drone-label-content">
                        <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                        <div class="drone-info">
                          ${drone.unite || 'N/A'}
                        </div>
                        <div class="drone-type-container">
                          <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                        </div>
                      </div>`,
                      iconSize: [220, 65], // Size for three lines with styling
                      iconAnchor: [0, 30]  // Adjusted anchor point
                    });

                    // Add the label marker at the offset position
                    const labelMarker = L.marker([labelLat, labelLng], {
                      icon: labelIcon,
                      zIndexOffset: 1000 // Ensure labels are above other markers
                    }).addTo(droneLayerGroup);

                    // Create a subtle line connecting the marker to the label
                    const connectorLine = L.polyline(
                      [
                        [lat, lng],
                        [labelLat, labelLng] // Use calculated offset position
                      ],
                      {
                        color: 'var(--text-secondary)', // Use theme color
                        weight: 0.8, // Very thin line
                        opacity: 0.5, // Subtle
                        dashArray: '2,4'
                      }
                    ).addTo(droneLayerGroup);

                    // Add popup with simplified drone details
                    droneMarker.bindPopup(`
                      <div class="drone-popup">
                        <h3>Drone #${drone.id}</h3>
                        <p>Type: ${drone.type_drone || 'N/A'}</p>
                        <p>Unité: ${drone.unite || 'N/A'}</p>
                        <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                      </div>
                    `);
                  }

                  console.log(`Refreshed ${droneMarkers.length} drone markers on map`);

                  console.log('Drone positions refreshed');
                } catch (error) {
                  console.error('Error refreshing drone positions:', error);
                }
              }, 30000); // Refresh every 30 seconds
            }

            // Helper function to get color based on drone type
            function getDroneColor(droneType) {
              const colorMap = {
                'Surveillance': '#1E90FF', // DodgerBlue
                'Reconnaissance': '#32CD32', // LimeGreen
                'Attack': '#FF4500', // OrangeRed
                'Transport': '#FFD700', // Gold
                'Medical': '#FF69B4', // HotPink
              };

              return colorMap[droneType] || '#808080'; // Default to gray if type not found
            }

            // Global function to restore default map settings for non-missile views
            function restoreDefaultMapSettings(map) {
              // Default settings (original map settings)
              const defaultCenter = [31.7917, -7.0926]; // Morocco default center
              const defaultZoom = 5; // Default zoom level

              console.log('Restoring default map view: center =', defaultCenter, 'zoom =', defaultZoom);

              // Only restore if we're coming from missile view
              if (map._currentView === 'missiles') {
                map.setView(defaultCenter, defaultZoom);

                // Remove missile legend when leaving missile view
                const existingLegend = document.querySelector('.missile-legend');
                if (existingLegend) {
                  existingLegend.remove();
                  console.log('Removed missile legend');
                }
              }

              // Update current view
              map._currentView = 'default';
            }

            // Global function to restore default map settings for non-missile views
            function restoreDefaultMapSettings(map) {
              // Default settings (original map settings)
              const defaultCenter = [31.7917, -7.0926]; // Morocco default center
              const defaultZoom = 5; // Default zoom level

              console.log('Restoring default map view: center =', defaultCenter, 'zoom =', defaultZoom);

              // Only restore if we're coming from missile view
              if (map._currentView === 'missiles') {
                map.setView(defaultCenter, defaultZoom);
              }

              // Update current view
              map._currentView = 'default';
            }

            // Helper function to calculate optimal label position to avoid overlaps
            function calculateLabelPosition(lat, lng, allMarkers, index, defaultOffset = 0.15) {
              // Define possible positions around the circle (8 directions)
              const positions = [
                { lat: defaultOffset, lng: 0 },            // North
                { lat: defaultOffset, lng: defaultOffset }, // Northeast
                { lat: 0, lng: defaultOffset },            // East
                { lat: -defaultOffset, lng: defaultOffset }, // Southeast
                { lat: -defaultOffset, lng: 0 },           // South
                { lat: -defaultOffset, lng: -defaultOffset }, // Southwest
                { lat: 0, lng: -defaultOffset },           // West
                { lat: defaultOffset, lng: -defaultOffset }  // Northwest
              ];

              // If this is the first marker or there are no other markers, use default Northeast position
              if (allMarkers.length <= 1 || index === 0) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Calculate distances to all other markers
              const distances = [];
              for (let i = 0; i < allMarkers.length; i++) {
                if (i !== index) {
                  const otherLat = allMarkers[i][0];
                  const otherLng = allMarkers[i][1];
                  const distance = Math.sqrt(
                    Math.pow(lat - otherLat, 2) +
                    Math.pow(lng - otherLng, 2)
                  );
                  distances.push({ index: i, distance });
                }
              }

              // Sort by distance (closest first)
              distances.sort((a, b) => a.distance - b.distance);

              // If closest marker is far enough away, use default position
              if (distances.length === 0 || distances[0].distance > defaultOffset * 3) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Get the closest marker
              const closestMarker = allMarkers[distances[0].index];
              const closestLat = closestMarker[0];
              const closestLng = closestMarker[1];

              // Determine which direction to place the label based on relative position
              // of the closest marker
              let bestPosition;

              if (closestLat > lat && closestLng > lng) {
                // Closest marker is Northeast, so place label Southwest
                bestPosition = positions[5];
              } else if (closestLat > lat && closestLng < lng) {
                // Closest marker is Northwest, so place label Southeast
                bestPosition = positions[3];
              } else if (closestLat < lat && closestLng > lng) {
                // Closest marker is Southeast, so place label Northwest
                bestPosition = positions[7];
              } else if (closestLat < lat && closestLng < lng) {
                // Closest marker is Southwest, so place label Northeast
                bestPosition = positions[1];
              } else if (closestLat > lat) {
                // Closest marker is North, so place label South
                bestPosition = positions[4];
              } else if (closestLat < lat) {
                // Closest marker is South, so place label North
                bestPosition = positions[0];
              } else if (closestLng > lng) {
                // Closest marker is East, so place label West
                bestPosition = positions[6];
              } else {
                // Closest marker is West, so place label East
                bestPosition = positions[2];
              }

              return {
                lat: lat + bestPosition.lat,
                lng: lng + bestPosition.lng
              };
            }

            // Function to fetch and display missile module deployments
            async function fetchAndDisplayMissileDeployments(map) {
              console.log('Fetching missile deployments...');

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch missile deployments from the database
                const response = await window.api.getMissileDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching missile deployments:', response.error);
                  alert('Error fetching missile data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Missile deployments fetched:', JSON.stringify(deployments));
                console.log(`Total missile records: ${deployments.length}`);

                // Remove existing missile layer group if it exists
                if (missileLayerGroup) {
                  map.removeLayer(missileLayerGroup);
                }

                // Create a new layer group for all missile markers
                missileLayerGroup = L.layerGroup().addTo(map);

                // Clear the missile markers array
                missileMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No missile deployments found in database');
                  alert('No missile deployments found in database');
                  return;
                }



                // Set custom zoom and center for missile view only
                const setMissileViewSettings = (map, markers) => {
                  console.log('Setting custom missile view: center =', MISSILE_VIEW_CENTER, 'zoom =', MISSILE_VIEW_ZOOM);

                  // Set the custom view for missiles using shared constants
                  map.setView(MISSILE_VIEW_CENTER, MISSILE_VIEW_ZOOM);
                  // Store that this is missile view for future reference
                  map._currentView = 'missiles';
                };



                // Group deployments by location to handle overlapping markers
                const locationGroups = {};

                // First pass: group deployments by coordinates or ville_implantation
                deployments.forEach((missile, index) => {
                  // Validate missile data - only require lat/lng for plotting
                  if (!missile.lat || !missile.lng) {
                    console.warn(`Skipping missile ${index+1} due to missing coordinates:`, missile);
                    return;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof missile.lat === 'string' ? parseFloat(missile.lat) : missile.lat;
                  const lng = typeof missile.lng === 'string' ? parseFloat(missile.lng) : missile.lng;

                  const ville = missile.ville_implantation || 'unknown';

                  // Create a key based on coordinates (rounded to avoid floating point issues) or ville
                  const coordKey = `${lat.toFixed(4)}_${lng.toFixed(4)}`;
                  const locationKey = `${ville}_${coordKey}`;

                  if (!locationGroups[locationKey]) {
                    locationGroups[locationKey] = [];
                  }
                  locationGroups[locationKey].push({ missile, originalIndex: index, lat, lng });
                });

                // Second pass: process each location group with spacing
                Object.keys(locationGroups).forEach(locationKey => {
                  const group = locationGroups[locationKey];

                  group.forEach((item, groupIndex) => {
                    const { missile, originalIndex, lat, lng } = item;

                    console.log(`Processing missile ${originalIndex+1}/${deployments.length}: ID=${missile.id}, Group=${groupIndex+1}/${group.length}`);

                    // Calculate offset for overlapping markers
                    let offsetLat = lat;
                    let offsetLng = lng;

                    if (group.length > 1) {
                      // Create circular arrangement for multiple markers at same location
                      const radius = 0.003; // Small radius for spacing (about 300m)
                      const angle = (groupIndex * 2 * Math.PI) / group.length;
                      offsetLat = lat + (radius * Math.cos(angle));
                      offsetLng = lng + (radius * Math.sin(angle));
                      console.log(`Applied offset for overlapping marker: [${offsetLat}, ${offsetLng}]`);
                    }

                    // Store the missile position for fitting bounds
                    missileMarkers.push([offsetLat, offsetLng]);

                    // Check if this is a D.M type (ammunition) - use special icon
                    if (missile.type === 'D.M') {
                      // Create ammunition icon for D.M type - brown color, bigger size
                      const ammunitionIcon = L.divIcon({
                        className: 'ammunition-icon',
                        html: `
                          <div class="ammunition-marker">
                            <i class="fas fa-boxes" style="color: #8B4513; font-size: 18px; text-shadow: 0 0 3px rgba(0,0,0,0.6);"></i>
                          </div>
                        `,
                        iconSize: [24, 24],
                        iconAnchor: [12, 12]
                      });

                      const missileMarker = L.marker([offsetLat, offsetLng], {
                        icon: ammunitionIcon,
                        zIndexOffset: 100
                      }).addTo(missileLayerGroup);
                    } else {
                      // Regular circle marker for non-D.M types
                      const isLightTheme = document.body.classList.contains('light-theme');
                      const circleColor = missile.materiel === 'Sky Dragon' ? '#ff0000' : '#ffff00'; // Red for Sky Dragon, Yellow for others

                      // Create simple dot marker - smaller size with colored circle
                      const missileMarker = L.circleMarker([offsetLat, offsetLng], {
                        radius: 3, // Smaller dot
                        fillColor: isLightTheme ? '#808080' : '#ffffff',
                        color: circleColor, // Colored circle based on materiel
                        weight: 2, // Slightly thicker border for visibility
                        opacity: 1,
                        fillOpacity: 0.9
                      }).addTo(missileLayerGroup);
                    }

                    console.log(`Added missile marker ${originalIndex+1} to map with offset`);
                  });
                });

                console.log(`Displayed ${missileMarkers.length} missile markers on map`);

                // Add Sidi Yahya location pin (current location marker)
                const sidiYahyaCoords = [34.30494, -6.30404]; // Sidi Yahya, Morocco coordinates
                const sidiYahyaIcon = L.divIcon({
                  className: 'sidi-yahya-pin',
                  html: `
                    <div class="location-pin">
                      <i class="fas fa-map-marker-alt" style="color: #00ff00; font-size: 20px; text-shadow: 0 0 3px rgba(0,0,0,0.5);"></i>
                    </div>
                  `,
                  iconSize: [20, 20],
                  iconAnchor: [10, 20]
                });

                const sidiYahyaMarker = L.marker(sidiYahyaCoords, {
                  icon: sidiYahyaIcon,
                  zIndexOffset: 1000 // Ensure it appears above other markers
                }).addTo(missileLayerGroup);

                console.log('Added Sidi Yahya location pin to map');

                // Set custom zoom and center for missile view
                setMissileViewSettings(directMap, missileMarkers);

              } catch (error) {
                console.error('Error displaying missile deployments:', error);
                alert('Error displaying missile deployments: ' + error.message);
              }
            }

            // Legend function removed as requested

            // Global variable to store drone markers for fitting bounds
            let droneMarkers = [];

            // Global variables for missile markers
            let missileLayerGroup = null;
            let missileMarkers = [];

            // Function to fit the map to show all drone markers (kept for potential future use)
            function fitMapToDrones(map) {
              if (droneMarkers.length === 0) {
                console.log('No drone markers to fit');
                return;
              }

              // Create a bounds object
              const bounds = L.latLngBounds(droneMarkers);

              // Fit the map to the bounds with some padding
              map.fitBounds(bounds, {
                padding: [50, 50],
                maxZoom: 8
              });

              console.log('Map fitted to show all drones');
            }

            // Global variable for mission arrow layer
            let missionArrowLayer = null;

            // Function to show missions panel and fetch mission data
            async function showMissionsPanel() {
              const missionsPanel = document.getElementById('missions-panel');
              const missionsList = document.getElementById('missions-list');

              if (missionsPanel) {
                missionsPanel.style.display = 'block';
                console.log('Missions panel shown');

                try {
                  // Fetch both mission data and interventions data
                  const [missionsResponse, interventionsResponse] = await Promise.all([
                    window.api.getMissionVerificationMissiles(),
                    window.api.getInterventions()
                  ]);

                  let allMissions = [];

                  // Process missile missions
                  if (missionsResponse.success) {
                    console.log('Missions fetched successfully:', missionsResponse.data);
                    // Add source type to distinguish between data sources
                    const missions = missionsResponse.data.map(mission => ({
                      ...mission,
                      source: 'missiles'
                    }));
                    allMissions = allMissions.concat(missions);
                  } else {
                    console.error('Failed to fetch missions:', missionsResponse.error);
                  }

                  // Process interventions
                  if (interventionsResponse.success) {
                    console.log('Interventions fetched successfully:', interventionsResponse.data);
                    // Transform interventions to match mission format
                    const interventions = interventionsResponse.data.map(intervention => ({
                      id: `intervention_${intervention.id}`, // Prefix to avoid ID conflicts
                      date: intervention.date_depart, // Use date_depart as primary date
                      type: intervention.nature_mission,
                      lieu_enlevement: intervention.unite_name || `Unité ${intervention.unite}`,
                      lieu_acheminement: intervention.unite_name || `Unité ${intervention.unite}`,
                      mission: intervention.operation,
                      // Additional intervention-specific fields
                      date_depart: intervention.date_depart,
                      date_retour: intervention.date_retour,
                      nature_mission: intervention.nature_mission,
                      unite: intervention.unite,
                      bie: intervention.bie,
                      operation: intervention.operation,
                      lat: intervention.lat,
                      lng: intervention.lng,
                      ville_implantation: intervention.ville_implantation,
                      // Additional module deployment fields
                      unite_name: intervention.unite_name,
                      batterie_module: intervention.batterie_module,
                      type: intervention.type,
                      unite_accueillante: intervention.unite_accueillante,
                      type_unite_accueillante: intervention.type_unite_accueillante,
                      source: 'interventions'
                    }));
                    allMissions = allMissions.concat(interventions);
                  } else {
                    console.error('Failed to fetch interventions:', interventionsResponse.error);
                  }

                  if (allMissions.length > 0) {
                    // Sort all missions by date
                    allMissions.sort((a, b) => {
                      const dateA = a.rescheduled_date || a.date;
                      const dateB = b.rescheduled_date || b.date;
                      return dateA.localeCompare(dateB);
                    });

                    console.log('All merged missions:', allMissions);
                    displayMissions(allMissions);

                    // Auto-select the first mission by default (after filtering and sorting)
                    // But only if there's exactly one current mission
                    setTimeout(() => {
                      if (window.currentMissions && window.currentMissions.length > 0) {
                        const today = new Date().toISOString().split('T')[0];

                        // Count current missions from the sorted missions
                        const actualCurrentMissions = window.currentMissions.filter(mission => {
                          if (mission.source === 'interventions') {
                            return (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                                   (!mission.date_retour && mission.date_depart <= today);
                          } else {
                            const missionDate = mission.rescheduled_date || mission.date;
                            return missionDate === today;
                          }
                        });

                        console.log(`Found ${actualCurrentMissions.length} current missions:`, actualCurrentMissions);

                        if (actualCurrentMissions.length === 1) {
                          // Only auto-select if there's exactly one current mission
                          console.log('Auto-selecting single current mission:', actualCurrentMissions[0]);
                          toggleMissionSelection(actualCurrentMissions[0].id);
                        } else if (actualCurrentMissions.length > 1) {
                          console.log(`Multiple current missions (${actualCurrentMissions.length}), showing all on map with tabs`);
                          // Display all current missions on map and show tabbed info
                          displayAllCurrentMissions(actualCurrentMissions);
                        } else {
                          // No current missions, auto-select first future mission
                          console.log('No current missions, auto-selecting first future mission:', window.currentMissions[0]);
                          toggleMissionSelection(window.currentMissions[0].id);
                        }
                      }
                    }, 200); // Increased delay to ensure displayMissions completes
                  } else {
                    missionsList.innerHTML = `<div class="mission-item">Aucune mission ou intervention trouvée</div>`;
                  }
                } catch (error) {
                  console.error('Error fetching missions and interventions:', error);
                  missionsList.innerHTML = `<div class="mission-item">Erreur: ${error.message}</div>`;
                }
              }
            }

            // Function to hide missions panel
            function hideMissionsPanel() {
              const missionsPanel = document.getElementById('missions-panel');
              if (missionsPanel) {
                missionsPanel.style.display = 'none';
                console.log('Missions panel hidden');
              }
            }

            // Function to calculate time difference in French format
            function getTimeDifference(missionDate, today) {
              const mission = new Date(missionDate);
              const todayDate = new Date(today);
              const diffTime = mission - todayDate;
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays === 0) {
                return "Aujourd'hui";
              } else if (diffDays === 1) {
                return "Demain";
              } else if (diffDays < 7) {
                return `Dans ${diffDays} jrs`;
              } else if (diffDays < 30) {
                const weeks = Math.floor(diffDays / 7);
                const remainingDays = diffDays % 7;
                if (remainingDays === 0) {
                  return `Dans ${weeks.toString().padStart(2, '0')} sem`;
                } else {
                  return `Dans ${weeks.toString().padStart(2, '0')} sem ${remainingDays.toString().padStart(2, '0')} jrs`;
                }
              } else {
                const months = Math.floor(diffDays / 30);
                const remainingDays = diffDays % 30;
                if (remainingDays === 0) {
                  return `Dans ${months.toString().padStart(2, '0')} mois`;
                } else if (remainingDays < 7) {
                  return `Dans ${months.toString().padStart(2, '0')} mois ${remainingDays} jrs`;
                } else {
                  const weeks = Math.floor(remainingDays / 7);
                  const finalDays = remainingDays % 7;
                  if (finalDays === 0) {
                    return `Dans ${months.toString().padStart(2, '0')} mois ${weeks.toString().padStart(2, '0')} sem`;
                  } else {
                    return `Dans ${months.toString().padStart(2, '0')} mois ${weeks.toString().padStart(2, '0')} sem ${finalDays} jrs`;
                  }
                }
              }
            }

            // Function to display missions in the panel
            function displayMissions(missions) {
              const missionsList = document.getElementById('missions-list');
              const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

              console.log('All missions received:', missions);
              console.log('Today date for filtering:', today);

              // Filter missions to show only current and future ones
              const filteredMissions = missions.filter(mission => {
                if (mission.source === 'interventions') {
                  // For interventions, use the same logic as database
                  const dateDepart = mission.date_depart;
                  const dateRetour = mission.date_retour;

                  // Current interventions: today between date_depart and date_retour (if exists)
                  // OR date_retour is null and date_depart <= today (ongoing)
                  const isCurrent = (dateRetour && today >= dateDepart && today <= dateRetour) ||
                                   (!dateRetour && dateDepart <= today);

                  // Future interventions: date_depart > today
                  const isFuture = dateDepart > today;

                  const isCurrentOrFuture = isCurrent || isFuture;
                  console.log(`Intervention ${mission.id}: date_depart=${dateDepart}, date_retour=${dateRetour}, isCurrent=${isCurrent}, isFuture=${isFuture}, isCurrentOrFuture=${isCurrentOrFuture}`);
                  return isCurrentOrFuture;
                } else {
                  // For missile missions, use existing logic
                  const missionDate = mission.rescheduled_date || mission.date;
                  const isCurrentOrFuture = missionDate >= today;
                  console.log(`Mission ${mission.id}: date=${missionDate}, isCurrentOrFuture=${isCurrentOrFuture}`);
                  return isCurrentOrFuture;
                }
              });

              console.log('Filtered missions (current/future only):', filteredMissions);

              // Sort missions: current missions first, then future missions by date
              const sortedMissions = filteredMissions.sort((a, b) => {
                // Get the primary date for each mission
                const dateA = a.source === 'interventions' ? a.date_depart : (a.rescheduled_date || a.date);
                const dateB = b.source === 'interventions' ? b.date_depart : (b.rescheduled_date || b.date);

                // Determine if each mission is current
                let isCurrentA, isCurrentB;

                if (a.source === 'interventions') {
                  isCurrentA = (a.date_retour && today >= a.date_depart && today <= a.date_retour) ||
                               (!a.date_retour && a.date_depart <= today);
                } else {
                  isCurrentA = dateA === today;
                }

                if (b.source === 'interventions') {
                  isCurrentB = (b.date_retour && today >= b.date_depart && today <= b.date_retour) ||
                               (!b.date_retour && b.date_depart <= today);
                } else {
                  isCurrentB = dateB === today;
                }

                // Current missions first
                if (isCurrentA && !isCurrentB) return -1;
                if (!isCurrentA && isCurrentB) return 1;

                // Within same category (current or future), sort by date
                return dateA.localeCompare(dateB);
              });

              console.log('Sorted missions (current first, then future):', sortedMissions);

              // Store sorted missions globally for hover functionality
              window.currentMissions = sortedMissions;

              if (sortedMissions.length === 0) {
                missionsList.innerHTML = '<div class="mission-item">Aucune mission programmée</div>';
                return;
              }

              // Group round-trip missions (using sorted missions)
              const groupedMissions = groupRoundTripMissions(sortedMissions);

              // Check if there are any current missions
              const currentMissions = sortedMissions.filter(mission => {
                if (mission.source === 'interventions') {
                  return (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                         (!mission.date_retour && mission.date_depart <= today);
                } else {
                  const missionDate = mission.rescheduled_date || mission.date;
                  return missionDate === today;
                }
              });

              // Find the first future mission for time indicator
              let firstFutureMissionFound = false;
              let firstFutureMissionId = null;

              for (const group of groupedMissions) {
                for (const mission of group.missions) {
                  const missionDate = mission.rescheduled_date || mission.date;
                  if (missionDate > today && !firstFutureMissionFound) {
                    firstFutureMissionId = mission.id;
                    firstFutureMissionFound = true;
                    break;
                  }
                }
                if (firstFutureMissionFound) break;
              }

              // Add placeholder for no current missions if needed
              let placeholderHtml = '';
              if (currentMissions.length === 0) {
                placeholderHtml = '<div class="no-current-mission-placeholder">Aucune mission n\'est en cours</div>';
              }

              const missionsHtml = groupedMissions.map(group => {
                if (group.isRoundTrip) {
                  // Render round-trip group
                  return `
                    <div class="mission-group">
                      ${group.missions.map((mission, index) => {
                        const missionDate = mission.source === 'interventions' ? mission.date_depart : (mission.rescheduled_date || mission.date);

                        let isCurrent, isFuture;
                        if (mission.source === 'interventions') {
                          isCurrent = (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                                     (!mission.date_retour && mission.date_depart <= today);
                          isFuture = mission.date_depart > today;
                        } else {
                          isCurrent = missionDate === today;
                          isFuture = missionDate > today;
                        }

                        const statusClass = isCurrent ? 'current' : (isFuture ? 'future' : '');
                        const tripType = index === 0 ? 'aller' : 'retour';

                        // Add time indicator for first future mission
                        const isFirstFuture = mission.id === firstFutureMissionId;
                        const timeIndicator = isFirstFuture ? `<div class="time-indicator">${getTimeDifference(missionDate, today)}</div>` : '';

                        // Add "En cours ..." label for current missions
                        const currentLabel = isCurrent ? `<div class="current-mission-label">En cours ...</div>` : '';

                        // Check if this is an intervention or a missile mission
                        if (mission.source === 'interventions') {
                          // Render intervention format with vertical dates and detailed location
                          const formatInterventionLocation = (intervention) => {
                            let locationParts = [];

                            // Add Batterie/module if exists
                            if (intervention.batterie_module) {
                              locationParts.push(intervention.batterie_module);
                            }

                            // Add Unite°Type if exists
                            let uniteTypeStr = '';
                            if (intervention.unite_name) {
                              uniteTypeStr = intervention.unite_name;
                              if (intervention.type) {
                                uniteTypeStr += `°${intervention.type}`;
                              }
                              locationParts.push(uniteTypeStr);
                            }

                            // Add Unité_accueillante°Type_unité_accueillante if exists
                            let accueillanteStr = '';
                            if (intervention.unite_accueillante) {
                              accueillanteStr = intervention.unite_accueillante;
                              if (intervention.type_unite_accueillante) {
                                accueillanteStr += `°${intervention.type_unite_accueillante}`;
                              }
                              locationParts.push(accueillanteStr);
                            }

                            return locationParts.join(' • ');
                          };

                          const locationDisplay = formatInterventionLocation(mission);

                          return `
                            <div class="mission-item ${statusClass} ${tripType} intervention"
                                 data-mission-id="${mission.id}"
                                 onmouseenter="showMissionArrowById('${mission.id}')"
                                 onmouseleave="showDefaultArrow()"
                                 onclick="toggleMissionSelection('${mission.id}')">
                              <div class="intervention-header">
                                <div class="intervention-date-container">
                                  <div class="intervention-date-label">du</div>
                                  <div class="intervention-date-depart">${formatDate(mission.date_depart)}</div>
                                  ${mission.date_retour ? `<div class="intervention-date-retour">au ${formatDate(mission.date_retour)}</div>` : ''}
                                </div>
                                <div class="intervention-header-right">
                                  <div class="mission-type">${mission.nature_mission || 'N/A'}</div>
                                  ${timeIndicator}
                                </div>
                              </div>
                              ${currentLabel}
                              <div class="mission-location">${locationDisplay || 'N/A'}</div>
                              <div class="intervention-details">
                                <div class="intervention-bie">${mission.bie || ''}</div>
                                <div class="intervention-operation">${mission.operation || ''}</div>
                              </div>
                            </div>
                          `;
                        } else {
                          // Render missile mission format
                          return `
                            <div class="mission-item ${statusClass} ${tripType}"
                                 data-mission-id="${mission.id}"
                                 onmouseenter="showMissionArrowById('${mission.id}')"
                                 onmouseleave="showDefaultArrow()"
                                 onclick="toggleMissionSelection('${mission.id}')">
                              <div class="mission-date">${formatDate(missionDate)}</div>
                              ${currentLabel}
                              <div class="mission-type">${mission.type || 'N/A'}</div>
                              ${timeIndicator}
                              ${mission.mission ? `<div class="mission-title">${mission.mission}</div>` : ''}
                              <div class="mission-route">
                                <span>${mission.lieu_enlevement || 'N/A'}</span>
                                <span class="route-separator">→</span>
                                <span>${mission.lieu_acheminement || 'N/A'}</span>
                              </div>
                            </div>
                          `;
                        }
                      }).join('')}
                    </div>
                  `;
                } else {
                  // Render single mission
                  const mission = group.missions[0];
                  const missionDate = mission.source === 'interventions' ? mission.date_depart : (mission.rescheduled_date || mission.date);

                  let isCurrent, isFuture;
                  if (mission.source === 'interventions') {
                    isCurrent = (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                               (!mission.date_retour && mission.date_depart <= today);
                    isFuture = mission.date_depart > today;
                  } else {
                    isCurrent = missionDate === today;
                    isFuture = missionDate > today;
                  }

                  const statusClass = isCurrent ? 'current' : (isFuture ? 'future' : '');

                  // Add time indicator for first future mission
                  const isFirstFuture = mission.id === firstFutureMissionId;
                  const timeIndicator = isFirstFuture ? `<div class="time-indicator">${getTimeDifference(missionDate, today)}</div>` : '';

                  // Add "En cours ..." label for current missions
                  const currentLabel = isCurrent ? `<div class="current-mission-label">En cours ...</div>` : '';

                  // Check if this is an intervention or a missile mission
                  if (mission.source === 'interventions') {
                    // Render intervention format with vertical dates and detailed location
                    const formatInterventionLocation = (intervention) => {
                      let locationParts = [];

                      // Add Batterie/module if exists
                      if (intervention.batterie_module) {
                        locationParts.push(intervention.batterie_module);
                      }

                      // Add Unite°Type if exists
                      let uniteTypeStr = '';
                      if (intervention.unite_name) {
                        uniteTypeStr = intervention.unite_name;
                        if (intervention.type) {
                          uniteTypeStr += `°${intervention.type}`;
                        }
                        locationParts.push(uniteTypeStr);
                      }

                      // Add Unité_accueillante°Type_unité_accueillante if exists
                      let accueillanteStr = '';
                      if (intervention.unite_accueillante) {
                        accueillanteStr = intervention.unite_accueillante;
                        if (intervention.type_unite_accueillante) {
                          accueillanteStr += `°${intervention.type_unite_accueillante}`;
                        }
                        locationParts.push(accueillanteStr);
                      }

                      return locationParts.join(' • ');
                    };

                    const locationDisplay = formatInterventionLocation(mission);

                    return `
                      <div class="mission-item ${statusClass} intervention"
                           data-mission-id="${mission.id}"
                           onmouseenter="showMissionArrowById('${mission.id}')"
                           onmouseleave="showDefaultArrow()"
                           onclick="toggleMissionSelection('${mission.id}')">
                        <div class="intervention-header">
                          <div class="intervention-date-container">
                            <div class="intervention-date-label">du</div>
                            <div class="intervention-date-depart">${formatDate(mission.date_depart)}</div>
                            ${mission.date_retour ? `<div class="intervention-date-retour">au ${formatDate(mission.date_retour)}</div>` : ''}
                          </div>
                          <div class="intervention-header-right">
                            <div class="mission-type">${mission.nature_mission || 'N/A'}</div>
                            ${timeIndicator}
                          </div>
                        </div>
                        ${currentLabel}
                        <div class="mission-location">${locationDisplay || 'N/A'}</div>
                        <div class="intervention-details">
                          <div class="intervention-bie">${mission.bie || ''}</div>
                          <div class="intervention-operation">${mission.operation || ''}</div>
                        </div>
                      </div>
                    `;
                  } else {
                    // Render missile mission format
                    return `
                      <div class="mission-item ${statusClass}"
                           data-mission-id="${mission.id}"
                           onmouseenter="showMissionArrowById('${mission.id}')"
                           onmouseleave="showDefaultArrow()"
                           onclick="toggleMissionSelection('${mission.id}')">
                        <div class="mission-date">${formatDate(missionDate)}</div>
                        ${currentLabel}
                        <div class="mission-type">${mission.type || 'N/A'}</div>
                        ${timeIndicator}
                        ${mission.mission ? `<div class="mission-title">${mission.mission}</div>` : ''}
                        <div class="mission-route">
                          <span>${mission.lieu_enlevement || 'N/A'}</span>
                          <span class="route-separator">→</span>
                          <span>${mission.lieu_acheminement || 'N/A'}</span>
                        </div>
                      </div>
                    `;
                  }
                }
              }).join('');

              // Combine placeholder and missions
              missionsList.innerHTML = placeholderHtml + missionsHtml;
            }

            // Global variable to track selected mission
            window.selectedMissionId = null;

            // Function to show mission arrow on hover
            window.showMissionArrowById = function(missionId) {
              // Don't change arrow on hover if a mission is selected
              if (window.selectedMissionId) return;

              console.log('showMissionArrowById called with ID:', missionId);
              console.log('Available missions:', window.currentMissions);

              if (!window.currentMissions) {
                console.log('No currentMissions available');
                return;
              }

              const mission = window.currentMissions.find(m => m.id == missionId);
              console.log('Found mission:', mission);

              if (mission) {
                console.log('Displaying arrow for mission:', mission.lieu_enlevement, '→', mission.lieu_acheminement);
                displayMissionArrow(mission);
                // Show mission info panel on hover
                showMissionInfo(mission);
              } else {
                console.log('Mission not found with ID:', missionId);
              }
            };

            // Function to show default arrow (appropriate default display)
            window.showDefaultArrow = function() {
              // Don't change arrow on hover if a mission is selected
              if (window.selectedMissionId) return;

              console.log('showDefaultArrow called');

              if (!window.currentMissions || window.currentMissions.length === 0) {
                console.log('No missions available for default arrow');
                // Actually hide mission info panel when no missions are available
                const display = document.getElementById('mission-info-display');
                const tabbedDisplay = document.getElementById('tabbed-mission-info-display');
                if (display) {
                  display.style.display = 'none';
                  display.classList.remove('show', 'hide');
                }
                if (tabbedDisplay) {
                  tabbedDisplay.style.display = 'none';
                  tabbedDisplay.classList.remove('show');
                }
                return;
              }

              // Use the hide mission info function which will trigger the appropriate default display
              hideMissionInfo();
            };

            // Function to toggle mission selection
            window.toggleMissionSelection = function(missionId) {
              console.log('toggleMissionSelection called with ID:', missionId);

              // Remove previous selection styling
              document.querySelectorAll('.mission-item.selected').forEach(item => {
                item.classList.remove('selected');
              });

              if (window.selectedMissionId === missionId) {
                // Deselect - return to appropriate default display
                window.selectedMissionId = null;
                console.log('Deselected mission, returning to default display');
                returnToDefaultMissionDisplay();
              } else {
                // Select new mission
                window.selectedMissionId = missionId;
                console.log('Selected mission ID:', missionId);

                // Add selected styling
                const selectedElement = document.querySelector(`[data-mission-id="${missionId}"]`);
                if (selectedElement) {
                  selectedElement.classList.add('selected');
                }

                // Show arrow for selected mission
                const mission = window.currentMissions.find(m => m.id == missionId);
                if (mission) {
                  displayMissionArrow(mission);
                  // Show mission info panel for selected mission
                  showMissionInfo(mission);
                }
              }
            };

            // Function to show tabbed mission info display for multiple current missions
            function showTabbedMissionInfo(currentMissions) {
              const tabbedDisplay = document.getElementById('tabbed-mission-info-display');
              const singleDisplay = document.getElementById('mission-info-display');

              if (!tabbedDisplay || currentMissions.length === 0) return;

              // Hide single mission display
              if (singleDisplay) {
                singleDisplay.style.display = 'none';
                singleDisplay.classList.remove('show');
              }

              // Create tabs
              const tabsContainer = document.getElementById('mission-tabs');
              if (!tabsContainer) return;

              tabsContainer.innerHTML = '';

              currentMissions.forEach((mission, index) => {
                const tab = document.createElement('div');
                tab.className = `mission-tab ${index === 0 ? 'active' : ''}`;
                tab.textContent = (index + 1).toString();
                tab.onclick = () => switchMissionTab(index, currentMissions);
                tabsContainer.appendChild(tab);
              });

              // Show first mission by default
              switchMissionTab(0, currentMissions);

              // Show tabbed display
              tabbedDisplay.style.display = 'flex';
              // Force reflow
              tabbedDisplay.offsetHeight;
              tabbedDisplay.classList.add('show');

              console.log('Tabbed mission info displayed for', currentMissions.length, 'current missions');
            }

            // Function to switch between mission tabs
            function switchMissionTab(tabIndex, currentMissions) {
              const mission = currentMissions[tabIndex];
              if (!mission) return;

              // Update tab active state
              const tabs = document.querySelectorAll('.mission-tab');
              tabs.forEach((tab, index) => {
                tab.classList.toggle('active', index === tabIndex);
              });

              // Update tab content with mission info
              updateTabMissionInfo(mission);
            }

            // Function to update tab mission info content
            function updateTabMissionInfo(mission) {
              console.log('updateTabMissionInfo called with mission:', mission);

              const today = new Date().toISOString().split('T')[0];

              // Format date
              const missionDate = mission.source === 'interventions' ? mission.date_depart : (mission.rescheduled_date || mission.date);
              const formattedDate = formatDate(missionDate);

              // Determine if mission is current
              let isCurrent;
              if (mission.source === 'interventions') {
                isCurrent = (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                           (!mission.date_retour && mission.date_depart <= today);
              } else {
                isCurrent = missionDate === today;
              }

              // Apply styling to tabbed display based on mission status
              const tabbedDisplay = document.getElementById('tabbed-mission-info-display');
              if (tabbedDisplay) {
                tabbedDisplay.classList.remove('current-mission', 'future-mission');
                if (isCurrent) {
                  tabbedDisplay.classList.add('current-mission');
                } else {
                  tabbedDisplay.classList.add('future-mission');
                }
              }

              // Get DOM elements
              const dateElement = document.getElementById('tab-mission-info-date');
              const typeElement = document.getElementById('tab-mission-info-type');
              const missionElement = document.getElementById('tab-mission-info-mission');
              const routeElement = document.getElementById('tab-mission-info-route');
              const villeElement = document.getElementById('tab-mission-info-ville');
              const bieElement = document.getElementById('tab-mission-info-bie');
              const statusElement = document.getElementById('tab-mission-info-status');

              console.log('Tab elements found:', {
                dateElement: !!dateElement,
                typeElement: !!typeElement,
                missionElement: !!missionElement,
                routeElement: !!routeElement,
                villeElement: !!villeElement,
                bieElement: !!bieElement,
                statusElement: !!statusElement
              });

              if (!dateElement) {
                console.error('tab-mission-info-date element not found!');
                return;
              }

              // Update content based on mission source
              if (mission.source === 'interventions') {
                // Handle intervention display
                const formatInterventionLocation = (intervention) => {
                  let locationParts = [];

                  if (intervention.batterie_module) {
                    locationParts.push(intervention.batterie_module);
                  }

                  let uniteTypeStr = '';
                  if (intervention.unite_name) {
                    uniteTypeStr = intervention.unite_name;
                    if (intervention.type) {
                      uniteTypeStr += `°${intervention.type}`;
                    }
                    locationParts.push(uniteTypeStr);
                  }

                  let accueillanteStr = '';
                  if (intervention.unite_accueillante) {
                    accueillanteStr = intervention.unite_accueillante;
                    if (intervention.type_unite_accueillante) {
                      accueillanteStr += `°${intervention.type_unite_accueillante}`;
                    }
                    locationParts.push(accueillanteStr);
                  }

                  return locationParts.join(' • ');
                };

                const dateDisplay = mission.date_retour
                  ? `du ${formatDate(mission.date_depart)}<br>au ${formatDate(mission.date_retour)}`
                  : `du ${formatDate(mission.date_depart)}`;

                const locationDisplay = formatInterventionLocation(mission);

                if (dateElement) {
                  if (isCurrent) {
                    dateElement.textContent = 'Aujourd\'hui';
                    console.log('Set date to: Aujourd\'hui');
                  } else {
                    dateElement.innerHTML = dateDisplay;
                    console.log('Set date to:', dateDisplay);
                  }
                }

                if (typeElement) {
                  typeElement.textContent = mission.nature_mission || 'N/A';
                  console.log('Set type to:', mission.nature_mission || 'N/A');
                }
                if (missionElement) {
                  missionElement.textContent = mission.operation || 'N/A';
                  console.log('Set mission to:', mission.operation || 'N/A');
                }
                if (routeElement) {
                  routeElement.textContent = locationDisplay || 'N/A';
                  console.log('Set route to:', locationDisplay || 'N/A');
                }

                if (villeElement) {
                  villeElement.innerHTML = `<i class="fas fa-map-marker-alt" style="color: #ff6b35; margin-right: 6px;"></i>${mission.ville_implantation || 'N/A'}`;
                  villeElement.style.display = 'block';
                  console.log('Set ville to:', `<location icon> ${mission.ville_implantation || 'N/A'}`);
                }

                if (bieElement) {
                  bieElement.textContent = `BIE: ${mission.bie || 'N/A'}`;
                  bieElement.style.display = 'block';
                  console.log('Set BIE to:', `BIE: ${mission.bie || 'N/A'}`);
                }
              } else {
                // Handle missile mission display
                if (dateElement) dateElement.textContent = isCurrent ? 'Aujourd\'hui' : formattedDate;
                if (typeElement) typeElement.textContent = mission.type || 'N/A';
                if (missionElement) missionElement.textContent = mission.mission || 'N/A';
                if (routeElement) routeElement.textContent = `${mission.lieu_enlevement || 'N/A'} → ${mission.lieu_acheminement || 'N/A'}`;

                if (villeElement) {
                  villeElement.textContent = '';
                  villeElement.style.display = 'none';
                }

                if (bieElement) {
                  bieElement.textContent = '';
                  bieElement.style.display = 'none';
                }
              }

              // Status display - show date difference
              if (statusElement) {
                if (isCurrent) {
                  statusElement.innerHTML = `
                    <div class="mission-status-live">
                      <span>En cours</span>
                      <div class="mission-status-dots">
                        <div class="mission-status-dot"></div>
                        <div class="mission-status-dot"></div>
                        <div class="mission-status-dot"></div>
                      </div>
                    </div>
                  `;
                } else {
                  // For future missions, show date difference
                  let futureMissionDate;
                  if (mission.source === 'interventions') {
                    futureMissionDate = mission.date_depart;
                  } else {
                    futureMissionDate = mission.rescheduled_date || mission.date;
                  }
                  const timeDiff = getTimeDifference(futureMissionDate, today);
                  statusElement.textContent = timeDiff;
                }
              }

              console.log('updateTabMissionInfo completed for mission:', mission.id);
            }

            // Function to show mission info display
            function showMissionInfo(mission) {
              const display = document.getElementById('mission-info-display');
              const tabbedDisplay = document.getElementById('tabbed-mission-info-display');
              if (!display) return;

              // Hide tabbed display if it's showing
              if (tabbedDisplay) {
                tabbedDisplay.style.display = 'none';
                tabbedDisplay.classList.remove('show');
              }

              // Cancel any pending hide operations
              window.missionInfoShouldHide = false;
              if (window.missionInfoHideTimeout) {
                clearTimeout(window.missionInfoHideTimeout);
                window.missionInfoHideTimeout = null;
              }

              const isFirstShow = !display.classList.contains('show');

              // Format date
              const missionDate = mission.rescheduled_date || mission.date;
              const formattedDate = formatDate(missionDate);

              // Determine if mission is current and apply color coding
              const today = new Date().toISOString().split('T')[0];
              let isCurrent;

              if (mission.source === 'interventions') {
                // For interventions, check date range
                isCurrent = (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                           (!mission.date_retour && mission.date_depart <= today);
              } else {
                // For missile missions, check single date
                isCurrent = missionDate === today;
              }

              // Apply color coding classes
              display.classList.remove('current-mission', 'future-mission');
              if (isCurrent) {
                display.classList.add('current-mission');
              } else {
                display.classList.add('future-mission');
              }

              // Update display content based on mission source
              if (mission.source === 'interventions') {
                // Handle intervention display
                const formatInterventionLocation = (intervention) => {
                  let locationParts = [];

                  // Add Batterie/module if exists
                  if (intervention.batterie_module) {
                    locationParts.push(intervention.batterie_module);
                  }

                  // Add Unite°Type if exists
                  let uniteTypeStr = '';
                  if (intervention.unite_name) {
                    uniteTypeStr = intervention.unite_name;
                    if (intervention.type) {
                      uniteTypeStr += `°${intervention.type}`;
                    }
                    locationParts.push(uniteTypeStr);
                  }

                  // Add Unité_accueillante°Type_unité_accueillante if exists
                  let accueillanteStr = '';
                  if (intervention.unite_accueillante) {
                    accueillanteStr = intervention.unite_accueillante;
                    if (intervention.type_unite_accueillante) {
                      accueillanteStr += `°${intervention.type_unite_accueillante}`;
                    }
                    locationParts.push(accueillanteStr);
                  }

                  return locationParts.join(' • ');
                };

                const dateDisplay = mission.date_retour
                  ? `du ${formatDate(mission.date_depart)}<br>au ${formatDate(mission.date_retour)}`
                  : `du ${formatDate(mission.date_depart)}`;

                const locationDisplay = formatInterventionLocation(mission);

                const dateElement = document.getElementById('mission-info-date');
                if (isCurrent) {
                  dateElement.textContent = 'Aujourd\'hui';
                } else {
                  dateElement.innerHTML = dateDisplay;
                }
                document.getElementById('mission-info-type').textContent = mission.nature_mission || 'N/A';
                document.getElementById('mission-info-mission').textContent = mission.operation || 'N/A';
                document.getElementById('mission-info-route').textContent = locationDisplay || 'N/A';

                // Add ville d'implantation for interventions
                const villeElement = document.getElementById('mission-info-ville');
                if (villeElement) {
                  villeElement.innerHTML = `<i class="fas fa-map-marker-alt" style="color: #ff6b35; margin-right: 6px;"></i>${mission.ville_implantation || 'N/A'}`;
                  villeElement.style.display = 'block';
                }

                // Add intervention-specific info if elements exist
                const bieElement = document.getElementById('mission-info-bie');
                if (bieElement) {
                  bieElement.textContent = `BIE: ${mission.bie || 'N/A'}`;
                  bieElement.style.display = 'block';
                }
              } else {
                // Handle missile mission display
                document.getElementById('mission-info-date').textContent = isCurrent ? 'Aujourd\'hui' : formattedDate;
                document.getElementById('mission-info-type').textContent = mission.type || 'N/A';
                document.getElementById('mission-info-mission').textContent = mission.mission || 'N/A';
                document.getElementById('mission-info-route').textContent =
                  `${mission.lieu_enlevement || 'N/A'} → ${mission.lieu_acheminement || 'N/A'}`;

                // Clear intervention-specific info if elements exist
                const villeElement = document.getElementById('mission-info-ville');
                if (villeElement) {
                  villeElement.textContent = '';
                  villeElement.style.display = 'none';
                }

                const bieElement = document.getElementById('mission-info-bie');
                if (bieElement) {
                  bieElement.textContent = '';
                  bieElement.style.display = 'none';
                }
              }

              // Status display - show date difference
              const statusElement = document.getElementById('mission-info-status');
              if (statusElement) {
                if (isCurrent) {
                  statusElement.innerHTML = `
                    <div class="mission-status-live">
                      <span>En cours</span>
                      <div class="mission-status-dots">
                        <div class="mission-status-dot"></div>
                        <div class="mission-status-dot"></div>
                        <div class="mission-status-dot"></div>
                      </div>
                    </div>
                  `;
                } else {
                  // For future missions, show date difference
                  let futureMissionDate;
                  if (mission.source === 'interventions') {
                    futureMissionDate = mission.date_depart;
                  } else {
                    futureMissionDate = mission.rescheduled_date || mission.date;
                  }
                  const timeDiff = getTimeDifference(futureMissionDate, today);
                  statusElement.textContent = timeDiff;
                }
              }

              // Show display with animation only on first show
              if (isFirstShow) {
                display.style.display = 'flex';
                // Force reflow to ensure display change is applied before animation
                display.offsetHeight;
                // Add show class for animation
                display.classList.remove('hide');
                display.classList.add('show');
              }
            }

            // Function to hide mission info display (now shows appropriate default)
            function hideMissionInfo() {
              const display = document.getElementById('mission-info-display');
              if (!display) return;

              // Set flag to indicate we want to return to default
              window.missionInfoShouldHide = true;

              // Clear any existing hide timeout
              if (window.missionInfoHideTimeout) {
                clearTimeout(window.missionInfoHideTimeout);
              }

              // Set a timeout to return to appropriate default display
              window.missionInfoHideTimeout = setTimeout(() => {
                // Only return to default if we still should (no new mission was hovered)
                if (window.missionInfoShouldHide) {
                  returnToDefaultMissionDisplay();
                }
                window.missionInfoHideTimeout = null;
              }, 200); // Delay to ensure smooth transitions
            }

            // Function to return to appropriate default mission display
            function returnToDefaultMissionDisplay() {
              if (!window.currentMissions) return;

              const today = new Date().toISOString().split('T')[0];

              // Count current missions
              const currentMissions = window.currentMissions.filter(mission => {
                if (mission.source === 'interventions') {
                  return (mission.date_retour && today >= mission.date_depart && today <= mission.date_retour) ||
                         (!mission.date_retour && mission.date_depart <= today);
                } else {
                  const missionDate = mission.rescheduled_date || mission.date;
                  return missionDate === today;
                }
              });

              if (currentMissions.length > 1) {
                // Multiple current missions: show all current missions and tabbed display
                console.log('Returning to multiple current missions display');
                displayAllCurrentMissions(currentMissions);
              } else if (currentMissions.length === 1) {
                // Single current mission: show first mission info
                console.log('Returning to single current mission display');
                showMissionInfo(currentMissions[0]);
              } else {
                // No current missions: show first future mission
                console.log('Returning to first future mission display');
                showFirstMissionInfo();
              }
            }

            // Function to show first mission info (default state)
            function showFirstMissionInfo() {
              // Get the first mission from the sorted list
              if (window.sortedMissions && window.sortedMissions.length > 0) {
                const firstMission = window.sortedMissions[0];
                showMissionInfo(firstMission);
              }
            }

            // Function to hide mission info display when switching away from missiles
            function hideMissionInfoOnToggleSwitch() {
              const display = document.getElementById('mission-info-display');
              const tabbedDisplay = document.getElementById('tabbed-mission-info-display');

              if (display && display.classList.contains('show')) {
                // Force hide immediately when switching toggles
                window.missionInfoShouldHide = true;
                if (window.missionInfoHideTimeout) {
                  clearTimeout(window.missionInfoHideTimeout);
                  window.missionInfoHideTimeout = null;
                }

                // Add hide class for animation
                display.classList.remove('show');
                display.classList.add('hide');

                // Hide after animation completes
                setTimeout(() => {
                  display.style.display = 'none';
                  display.classList.remove('hide');
                }, 300); // Match the hide animation duration
              }

              // Also hide tabbed display
              if (tabbedDisplay && tabbedDisplay.classList.contains('show')) {
                tabbedDisplay.classList.remove('show');
                setTimeout(() => {
                  tabbedDisplay.style.display = 'none';
                }, 300);
                console.log('Tabbed mission info display hidden on toggle switch');
              }

              // Also clear selected mission
              window.selectedMissionId = null;
              // Remove selected styling from all mission items
              document.querySelectorAll('.mission-item.selected').forEach(item => {
                item.classList.remove('selected');
              });
            }

            // Function to group round-trip missions
            function groupRoundTripMissions(missions) {
              const groups = [];
              const used = new Set();

              for (let i = 0; i < missions.length; i++) {
                if (used.has(i)) continue;

                const mission1 = missions[i];
                const date1 = new Date(mission1.rescheduled_date || mission1.date);

                // Look for return trip (next day, reversed route)
                let foundReturn = false;
                for (let j = i + 1; j < missions.length; j++) {
                  if (used.has(j)) continue;

                  const mission2 = missions[j];
                  const date2 = new Date(mission2.rescheduled_date || mission2.date);

                  // Check if it's next day and reversed route
                  const dayDiff = Math.abs(date2 - date1) / (1000 * 60 * 60 * 24);
                  const isNextDay = dayDiff >= 1 && dayDiff <= 2; // Allow 1-2 days difference
                  const isReversedRoute = (
                    mission1.lieu_enlevement === mission2.lieu_acheminement &&
                    mission1.lieu_acheminement === mission2.lieu_enlevement
                  );

                  if (isNextDay && isReversedRoute) {
                    // Found round trip
                    const sortedMissions = date1 < date2 ? [mission1, mission2] : [mission2, mission1];
                    groups.push({
                      isRoundTrip: true,
                      missions: sortedMissions
                    });
                    used.add(i);
                    used.add(j);
                    foundReturn = true;
                    break;
                  }
                }

                if (!foundReturn) {
                  // Single mission
                  groups.push({
                    isRoundTrip: false,
                    missions: [mission1]
                  });
                  used.add(i);
                }
              }

              return groups;
            }

            // Function to calculate curved path between two points
            function calculateCurvedPath(startPoint, endPoint) {
              const lat1 = startPoint[0];
              const lng1 = startPoint[1];
              const lat2 = endPoint[0];
              const lng2 = endPoint[1];

              // Calculate midpoint
              const midLat = (lat1 + lat2) / 2;
              const midLng = (lng1 + lng2) / 2;

              // Calculate distance between points
              const distance = Math.sqrt(Math.pow(lat2 - lat1, 2) + Math.pow(lng2 - lng1, 2));

              // Calculate curve offset (perpendicular to the line)
              const curveOffset = distance * 0.3; // 30% of distance for curve height

              // Calculate perpendicular direction
              const dx = lng2 - lng1;
              const dy = lat2 - lat1;
              const perpLat = -dx / Math.sqrt(dx * dx + dy * dy) * curveOffset;
              const perpLng = dy / Math.sqrt(dx * dx + dy * dy) * curveOffset;

              // Control point for the curve (offset from midpoint)
              const controlLat = midLat + perpLat;
              const controlLng = midLng + perpLng;

              // Generate curved path using quadratic bezier curve
              const pathPoints = [];
              const numPoints = 20; // Number of points for smooth curve

              for (let i = 0; i <= numPoints; i++) {
                const t = i / numPoints;
                const lat = Math.pow(1 - t, 2) * lat1 + 2 * (1 - t) * t * controlLat + Math.pow(t, 2) * lat2;
                const lng = Math.pow(1 - t, 2) * lng1 + 2 * (1 - t) * t * controlLng + Math.pow(t, 2) * lng2;
                pathPoints.push([lat, lng]);
              }

              return pathPoints;
            }

            // Function to display all current missions on map and show tabbed info
            function displayAllCurrentMissions(currentMissions) {
              // Use the passed currentMissions array to ensure consistent ordering
              if (!currentMissions || currentMissions.length === 0) return;

              console.log('Displaying all current missions:', currentMissions);

              // Clear existing arrow/target
              if (missionArrowLayer) {
                directMap.removeLayer(missionArrowLayer);
              }

              // Create a layer group for all current missions
              const allCurrentMissionsLayers = [];

              currentMissions.forEach((mission, index) => {
                if (mission.source === 'interventions') {
                  // Display intervention target
                  if (mission.lat && mission.lng) {
                    const targetPoint = [parseFloat(mission.lat), parseFloat(mission.lng)];

                    // Create crosshair icon with number for multiple missions
                    const crosshairColor = '#00ff00'; // Green for current missions
                    const targetIcon = L.divIcon({
                      className: 'intervention-crosshair multiple-current',
                      html: `
                        <div class="crosshair-container current">
                          <div class="crosshair-lines" style="color: ${crosshairColor};">
                            <div class="crosshair-horizontal"></div>
                            <div class="crosshair-vertical"></div>
                          </div>
                          <div class="implantation-circle" style="border-color: ${crosshairColor};">
                            <span class="unite-number">${index + 1}</span>
                          </div>
                        </div>
                      `,
                      iconSize: [40, 40],
                      iconAnchor: [20, 20]
                    });

                    const targetMarker = L.marker(targetPoint, {
                      icon: targetIcon,
                      zIndexOffset: 1000 + index
                    });

                    // Create formatted location label using the same function
                    const formatInterventionLabel = (intervention) => {
                      let firstLineParts = [];

                      // Add batterie/module if exists
                      if (intervention.batterie_module) {
                        firstLineParts.push(intervention.batterie_module);
                      }

                      // Add unite°type if both exist
                      if (intervention.unite_name && intervention.type) {
                        firstLineParts.push(`${intervention.unite_name}°${intervention.type}`);
                      }

                      // Add ville d'implantation
                      if (intervention.ville_implantation) {
                        firstLineParts.push(intervention.ville_implantation);
                      }

                      let labelContent = firstLineParts.join(' • ');

                      // Add opération on new line if exists
                      if (intervention.operation) {
                        labelContent += `<br>${intervention.operation}`;
                      }

                      return labelContent;
                    };

                    const locationLabel = L.divIcon({
                      className: 'intervention-location-label current-multiple',
                      html: `<div class="location-label-content discreet">${formatInterventionLabel(mission)}</div>`,
                      iconSize: [200, 40], /* Increased height for two lines */
                      iconAnchor: [100, 55] /* Adjusted anchor for better positioning */
                    });

                    const labelMarker = L.marker(targetPoint, {
                      icon: locationLabel,
                      zIndexOffset: 999 + index
                    });

                    allCurrentMissionsLayers.push(targetMarker, labelMarker);
                  }
                } else {
                  // Display missile mission arrow
                  if (mission.pickup_lat && mission.pickup_lng && mission.delivery_lat && mission.delivery_lng) {
                    const startPoint = [parseFloat(mission.pickup_lat), parseFloat(mission.pickup_lng)];
                    const endPoint = [parseFloat(mission.delivery_lat), parseFloat(mission.delivery_lng)];

                    // Create curved arrow line with number
                    const arrowOptions = {
                      color: '#00ff00',
                      weight: 3,
                      opacity: 1
                    };

                    const curvedPath = calculateCurvedPath(startPoint, endPoint);
                    const arrowLine = L.polyline(curvedPath, arrowOptions);

                    // Add numbered arrow head
                    const lastPoint = curvedPath[curvedPath.length - 1];
                    const secondLastPoint = curvedPath[curvedPath.length - 2];
                    const arrowAngle = calculateAngle(secondLastPoint, lastPoint);

                    const arrowHeadIcon = L.divIcon({
                      className: 'arrow-head current-multiple',
                      html: `<div style="color: #00ff00; font-size: 16px; transform: rotate(${arrowAngle}deg);">${index + 1}▶</div>`,
                      iconSize: [25, 25],
                      iconAnchor: [12, 12]
                    });

                    const arrowHead = L.marker(lastPoint, { icon: arrowHeadIcon });

                    // Add location markers with numbers
                    const pickupMarker = L.circleMarker(startPoint, {
                      radius: 10,
                      fillColor: '#00ff00',
                      color: '#ffffff',
                      weight: 2,
                      opacity: 1,
                      fillOpacity: 0.8
                    });

                    const deliveryMarker = L.circleMarker(endPoint, {
                      radius: 10,
                      fillColor: '#00ff00',
                      color: '#ffffff',
                      weight: 2,
                      opacity: 1,
                      fillOpacity: 0.8
                    });

                    allCurrentMissionsLayers.push(arrowLine, arrowHead, pickupMarker, deliveryMarker);
                  }
                }
              });

              // Add all layers to map
              if (allCurrentMissionsLayers.length > 0) {
                missionArrowLayer = L.layerGroup(allCurrentMissionsLayers).addTo(directMap);
              }

              // Show tabbed mission info
              showTabbedMissionInfo(currentMissions);
            }

            // Function to display intervention target on map
            function displayInterventionTarget(intervention) {
              if (!directMap || !intervention.lat || !intervention.lng) {
                console.log('Cannot display intervention target: missing coordinates or map');
                return;
              }

              // Clear existing arrow
              if (missionArrowLayer) {
                directMap.removeLayer(missionArrowLayer);
              }

              const today = new Date().toISOString().split('T')[0];
              const isCurrent = (intervention.date_retour && today >= intervention.date_depart && today <= intervention.date_retour) ||
                               (!intervention.date_retour && intervention.date_depart <= today);

              const targetPoint = [parseFloat(intervention.lat), parseFloat(intervention.lng)];

              console.log('Intervention target coordinates:', {
                point: targetPoint,
                location: intervention.lieu_enlevement,
                unite: intervention.unite
              });

              // Find the mission's position in the current missions list for correct numbering
              let displayNumber = '';
              if (window.currentMissions) {
                const missionIndex = window.currentMissions.findIndex(m => m.id === intervention.id);
                if (missionIndex !== -1) {
                  displayNumber = missionIndex + 1; // 1-based indexing
                }
              }

              // Create crosshair icon with pulsing animation
              const crosshairColor = isCurrent ? '#00ff00' : '#888888'; // Green for current, grey for others
              const targetIcon = L.divIcon({
                className: 'intervention-crosshair',
                html: `
                  <div class="crosshair-container ${isCurrent ? 'current' : 'future'}">
                    <div class="crosshair-lines" style="color: ${crosshairColor};">
                      <div class="crosshair-horizontal"></div>
                      <div class="crosshair-vertical"></div>
                    </div>
                    <div class="implantation-circle" style="border-color: ${crosshairColor};">
                      <span class="unite-number">${displayNumber}</span>
                    </div>
                  </div>
                `,
                iconSize: [40, 40],
                iconAnchor: [20, 20]
              });

              // Add target marker
              const targetMarker = L.marker(targetPoint, {
                icon: targetIcon,
                zIndexOffset: 1000
              }).addTo(directMap);

              // Create formatted location label for intervention
              const formatInterventionLabel = (intervention) => {
                let firstLineParts = [];

                // Add batterie/module if exists
                if (intervention.batterie_module) {
                  firstLineParts.push(intervention.batterie_module);
                }

                // Add unite°type if both exist
                if (intervention.unite_name && intervention.type) {
                  firstLineParts.push(`${intervention.unite_name}°${intervention.type}`);
                }

                // Add ville d'implantation
                if (intervention.ville_implantation) {
                  firstLineParts.push(intervention.ville_implantation);
                }

                let labelContent = firstLineParts.join(' • ');

                // Add opération on new line if exists
                if (intervention.operation) {
                  labelContent += `<br>${intervention.operation}`;
                }

                return labelContent;
              };

              const locationLabel = L.divIcon({
                className: 'intervention-location-label',
                html: `<div class="location-label-content discreet">${formatInterventionLabel(intervention)}</div>`,
                iconSize: [200, 40], /* Increased height for two lines */
                iconAnchor: [100, 55] /* Adjusted anchor for better positioning */
              });

              // Add location label
              const labelMarker = L.marker(targetPoint, {
                icon: locationLabel,
                zIndexOffset: 999
              }).addTo(directMap);

              // Group target and label
              missionArrowLayer = L.layerGroup([targetMarker, labelMarker]).addTo(directMap);

              console.log('Intervention target displayed successfully');
            }

            // Function to display mission arrow on map
            function displayMissionArrow(mission) {
              // Handle interventions differently
              if (mission.source === 'interventions') {
                displayInterventionTarget(mission);
                return;
              }

              if (!directMap || !mission.pickup_lat || !mission.pickup_lng || !mission.delivery_lat || !mission.delivery_lng) {
                console.log('Cannot display mission arrow: missing coordinates or map');
                return;
              }

              // Clear existing arrow
              if (missionArrowLayer) {
                directMap.removeLayer(missionArrowLayer);
              }

              const today = new Date().toISOString().split('T')[0];
              const missionDate = mission.rescheduled_date || mission.date;
              const isCurrent = missionDate === today;

              const startPoint = [parseFloat(mission.pickup_lat), parseFloat(mission.pickup_lng)];
              const endPoint = [parseFloat(mission.delivery_lat), parseFloat(mission.delivery_lng)];

              console.log('Arrow coordinates:', {
                start: startPoint,
                end: endPoint,
                pickup_location: mission.lieu_enlevement,
                delivery_location: mission.lieu_acheminement
              });

              // Create curved arrow line
              const arrowOptions = {
                color: isCurrent ? '#00ff00' : '#888888',
                weight: isCurrent ? 3 : 2,
                opacity: isCurrent ? 1 : 0.7,
                dashArray: isCurrent ? null : '10, 10'
              };

              // Calculate curved path points
              const curvedPath = calculateCurvedPath(startPoint, endPoint);
              missionArrowLayer = L.polyline(curvedPath, arrowOptions).addTo(directMap);

              // Add arrow head using a marker at the end of the curved path
              // Calculate angle from the last two points of the curve for proper direction
              const lastPoint = curvedPath[curvedPath.length - 1];
              const secondLastPoint = curvedPath[curvedPath.length - 2];
              const arrowAngle = calculateAngle(secondLastPoint, lastPoint);

              // Find the mission's position in the current missions list for correct numbering
              let displayNumber = '';
              if (window.currentMissions) {
                const missionIndex = window.currentMissions.findIndex(m => m.id === mission.id);
                if (missionIndex !== -1) {
                  displayNumber = missionIndex + 1; // 1-based indexing
                }
              }

              const arrowHeadIcon = L.divIcon({
                className: 'arrow-head',
                html: `<div style="color: ${arrowOptions.color}; font-size: 16px; transform: rotate(${arrowAngle}deg);">${displayNumber}▶</div>`,
                iconSize: [25, 25],
                iconAnchor: [12, 12]
              });

              const arrowHead = L.marker(lastPoint, { icon: arrowHeadIcon }).addTo(directMap);

              // Create floating labels for pickup and delivery locations
              const pickupLabel = createLocationLabel(mission, 'pickup');
              const deliveryLabel = createLocationLabel(mission, 'delivery');

              // Add location markers (circles) for pickup and delivery
              const pickupMarker = L.circleMarker(startPoint, {
                radius: 8,
                fillColor: isCurrent ? '#00ff00' : '#888888',
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
              }).addTo(directMap);

              const deliveryMarker = L.circleMarker(endPoint, {
                radius: 8,
                fillColor: isCurrent ? '#00ff00' : '#888888',
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
              }).addTo(directMap);

              // Add floating labels next to the markers
              const pickupLabelMarker = L.marker(startPoint, {
                icon: pickupLabel,
                zIndexOffset: 1000
              }).addTo(directMap);

              const deliveryLabelMarker = L.marker(endPoint, {
                icon: deliveryLabel,
                zIndexOffset: 1000
              }).addTo(directMap);

              // Group arrow line, head, markers, and labels
              missionArrowLayer = L.layerGroup([
                missionArrowLayer,
                arrowHead,
                pickupMarker,
                deliveryMarker,
                pickupLabelMarker,
                deliveryLabelMarker
              ]).addTo(directMap);

              console.log(`Mission arrow displayed: ${isCurrent ? 'current (green)' : 'future (grey dashed)'}`);
            }

            // Helper function to create location label
            function createLocationLabel(mission, locationType) {
              const isPickup = locationType === 'pickup';
              const prefix = isPickup ? 'pickup_' : 'delivery_';

              // Build label content based on available data with specific formatting
              const labelParts = [];

              // Add Batterie/module if exists
              const batterieModule = mission[prefix + 'batterie_module'];
              if (batterieModule) {
                labelParts.push(batterieModule);
              }

              // Add Unité and Type with "°" between them if both exist
              const unite = mission[prefix + 'unite'];
              const type = mission[prefix + 'type'];
              if (unite && type) {
                labelParts.push(`${unite}°${type}`);
              } else if (unite) {
                labelParts.push(unite);
              } else if (type) {
                labelParts.push(type);
              }

              // Add Unité_accueillante and Type_unité_accueillante with "°" between them if both exist
              const uniteAccueillante = mission[prefix + 'unite_accueillante'];
              const typeUniteAccueillante = mission[prefix + 'type_unite_accueillante'];
              if (uniteAccueillante && typeUniteAccueillante) {
                labelParts.push(`${uniteAccueillante}°${typeUniteAccueillante}`);
              } else if (uniteAccueillante) {
                labelParts.push(uniteAccueillante);
              } else if (typeUniteAccueillante) {
                labelParts.push(typeUniteAccueillante);
              }

              // If no data available, show location name
              const locationName = isPickup ? mission.lieu_enlevement : mission.lieu_acheminement;
              const labelText = labelParts.length > 0 ? labelParts.join(' • ') : locationName;

              // Create floating label icon with proper arrow direction
              const arrowClass = isPickup ? 'arrow-right' : 'arrow-left';

              return L.divIcon({
                className: 'location-info-label',
                html: `
                  <div class="location-label-content ${isPickup ? 'pickup-label' : 'delivery-label'}">
                    ${isPickup ? `<div class="location-label-text">${labelText}</div>` : `<div class="location-label-arrow ${arrowClass}"></div>`}
                    ${isPickup ? `<div class="location-label-arrow ${arrowClass}"></div>` : `<div class="location-label-text">${labelText}</div>`}
                  </div>
                `,
                iconSize: [250, 30],
                iconAnchor: [isPickup ? -15 : 265, 15] // Closer positioning to the marker
              });
            }

            // Helper function to calculate angle between two points
            function calculateAngle(start, end) {
              const dx = end[1] - start[1];
              const dy = end[0] - start[0];
              return Math.atan2(dy, dx) * 180 / Math.PI;
            }

            // Helper function to format date
            function formatDate(dateString) {
              if (!dateString) return 'Date non définie';
              const date = new Date(dateString);
              return date.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
              });
            }

            // Function to clear all data layers from the map
            function clearAllMapLayers(map) {
              console.log('Clearing all map layers...');

              // Clear drone layer if it exists
              if (droneLayerGroup) {
                map.removeLayer(droneLayerGroup);
                droneLayerGroup = null;
                droneMarkers = [];
                console.log('Drone layer removed');
              }

              // Clear missile layer if it exists
              if (missileLayerGroup) {
                map.removeLayer(missileLayerGroup);
                missileLayerGroup = null;
                missileMarkers = [];
                console.log('Missile layer removed');
              }

              // Clear mission arrow if it exists
              if (missionArrowLayer) {
                map.removeLayer(missionArrowLayer);
                missionArrowLayer = null;
                console.log('Mission arrow removed');
              }

              // Hide missions panel
              hideMissionsPanel();

              // Add more layer clearing here as needed for future layer types

              // Show the GSA logo overlay when no layers are active
              const mapLogoOverlay = document.getElementById('map-logo-overlay');
              if (mapLogoOverlay) {
                mapLogoOverlay.style.display = 'block';
              }

              // Ensure map is visible and properly sized
              const mapElement = document.getElementById('morocco-map');
              if (mapElement && directMap) {
                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                directMap.invalidateSize();

                // Reset view to ensure Morocco is centered
                directMap.setView(initialCenter, initialZoom);
              }

              console.log('All map layers cleared');
            }

            // Function to add a zoom level display to the map
            function addZoomLevelDisplay(map) {
              // Create a custom control that will appear next to the zoom controls
              const zoomDisplay = L.control({ position: 'topleft' });

              // When the control is added to the map
              zoomDisplay.onAdd = function(map) {
                // Create a container for both the reset button and zoom display
                const container = L.DomUtil.create('div', 'map-controls-container');

                // Create the reset button
                const resetButton = L.DomUtil.create('button', 'map-reset-button', container);
                resetButton.innerHTML = '<i class="fas fa-crosshairs"></i>'; // Changed to a crosshairs/target icon for recenter
                resetButton.title = 'Recenter map';

                // Add click event listener to reset button
                L.DomEvent.on(resetButton, 'click', function(e) {
                  L.DomEvent.stopPropagation(e);
                  L.DomEvent.preventDefault(e);

                  // Check if we're in missile view and use appropriate center/zoom
                  let centerCoords, zoomLevel;
                  if (map._currentView === 'missiles') {
                    // Use the exact same constants as missile view
                    centerCoords = MISSILE_VIEW_CENTER;
                    zoomLevel = MISSILE_VIEW_ZOOM;
                    console.log('Reset button: Using missile view settings', centerCoords, zoomLevel);
                  } else {
                    // Use default map settings
                    centerCoords = initialCenter;
                    zoomLevel = initialZoom;
                    console.log('Reset button: Using default view settings', centerCoords, zoomLevel);
                  }

                  // Reset the map view to the appropriate center and zoom
                  map.setView(centerCoords, zoomLevel, {
                    animate: true,
                    duration: 1.0,
                    noMoveStart: true
                  });

                  // Ensure the zoom is exactly the target zoom after a short delay
                  setTimeout(() => {
                    if (map.getZoom() !== zoomLevel) {
                      map.setZoom(zoomLevel);
                      map.fire('zoomend');
                    }
                    console.log('Map view reset to position with zoom level:', map.getZoom());
                  }, 100);
                });

                // Create the zoom level display
                const zoomLevelDisplay = L.DomUtil.create('div', 'zoom-level-display', container);
                zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;

                // Update the zoom level display whenever the zoom changes
                map.on('zoomend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Also update on moveend to catch any zoom changes from other sources
                map.on('moveend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Prevent map click events when clicking the controls
                L.DomEvent.disableClickPropagation(container);

                return container;
              };

              // Add the control to the map
              zoomDisplay.addTo(map);
            }

            // Reset button functionality has been integrated into the zoom level display function

            // Function to set up map toggle buttons
            function setupMapToggleButtons() {
              const toggleButtons = document.querySelectorAll('.map-toggle-btn');
              const mapLogoOverlay = document.getElementById('map-logo-overlay');

              // Function to ensure map is visible and properly sized
              function ensureMapIsVisible() {
                // Get the map element
                const mapElement = document.getElementById('morocco-map');
                if (!mapElement) return;

                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                if (directMap) {
                  console.log('Refreshing map size');
                  directMap.invalidateSize();

                  // Reset view to ensure Morocco is centered
                  directMap.setView(initialCenter, initialZoom);
                }
              }

              // Function to check if any toggle button is active
              function isAnyToggleActive() {
                return Array.from(toggleButtons).some(btn => btn.classList.contains('active'));
              }

              // Function to update logo visibility
              function updateLogoVisibility() {
                if (isAnyToggleActive()) {
                  // Hide logo when any toggle is active
                  mapLogoOverlay.style.display = 'none';
                } else {
                  // Show logo when no toggle is active
                  mapLogoOverlay.style.display = 'block';
                }
              }

              // Initial setup - ensure map is visible and show logo if no toggle is active
              ensureMapIsVisible();
              updateLogoVisibility();

              // Add click event listeners to each button
              toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                  const layer = this.getAttribute('data-layer');
                  const isCurrentlyActive = this.classList.contains('active');

                  // First, remove active class from all buttons
                  toggleButtons.forEach(btn => {
                    btn.classList.remove('active');
                  });

                  // First, clear all layers from the map regardless of which button was clicked
                  clearAllMapLayers(directMap);

                  // Hide mission info display when switching away from missiles
                  hideMissionInfoOnToggleSwitch();

                  // Ensure map is visible
                  ensureMapIsVisible();

                  // If the clicked button wasn't already active, make it active and display its data
                  if (!isCurrentlyActive) {
                    this.classList.add('active');
                    console.log(`Toggle ${layer} layer: ON`);

                    // Handle different layer types
                    switch(layer) {
                      case 'missiles':
                        // Fetch and display missile deployments
                        fetchAndDisplayMissileDeployments(directMap);
                        // Show missions panel and fetch mission data
                        showMissionsPanel();
                        break;
                      case 'drones':
                        // Fetch and display drone deployments
                        fetchAndDisplayDroneDeployments(directMap);
                        // Hide missions panel for non-missile views
                        hideMissionsPanel();
                        break;
                      case 'personnel':
                        console.log('Personnel drone layer not implemented yet');
                        // Restore default map settings for personnel view
                        restoreDefaultMapSettings(directMap);
                        // Hide missions panel for non-missile views
                        hideMissionsPanel();
                        break;
                      case 'stats':
                        console.log('Statistics layer not implemented yet');
                        // Restore default map settings for statistics view
                        restoreDefaultMapSettings(directMap);
                        // Hide missions panel for non-missile views
                        hideMissionsPanel();
                        break;
                      default:
                        console.log(`Unknown layer type: ${layer}`);
                    }
                  } else {
                    // If the button was already active, just turn it off
                    // The map is already cleared at the beginning of this function
                    console.log(`Toggle ${layer} layer: OFF`);
                  }

                  // Update logo visibility based on toggle state
                  updateLogoVisibility();
                });
              });

              console.log('Map toggle buttons initialized');
            }

            // No need for a popup here as it's handled in the GeoJSON implementation

            // Map bounds are set in the GeoJSON implementation

            // ===== FORMATIONS INITIALES FUNCTIONS =====
            // Variables globales pour les formations
            let currentFormation = null;
            let currentSpecialite = null;
            let formationsData = {};
            let allFormations = [];
            let currentSpecialites = [];

            // Charger les données des formations depuis la base de données
            async function initializeFormationsData() {
              try {
                console.log('🔄 FORMATIONS: Loading formations initiales from database...');
                const response = await window.api.getFormationsInitiales();

                if (response && response.success) {
                  allFormations = response.data;
                  console.log('Loaded formations:', allFormations);

                  // Populate formation selector
                  const formationSelector = document.getElementById('formation-selector');
                  formationSelector.innerHTML = '<option value="">Sélectionner une formation...</option>';

                  allFormations.forEach(formation => {
                    const option = document.createElement('option');
                    option.value = formation.id;
                    option.textContent = `${formation.code} - ${formation.nom}`;
                    formationSelector.appendChild(option);
                  });

                  // Auto-select first formation if available
                  if (allFormations.length > 0) {
                    console.log('🔄 FORMATIONS: Auto-selecting first formation:', allFormations[0]);
                    formationSelector.value = allFormations[0].id;
                    currentFormation = allFormations[0];

                    // Load specialites for the first formation with auto-selection
                    await loadSpecialitesForFormation(allFormations[0].id, true);

                    // Enable specialty selector
                    const specialtySelector = document.getElementById('specialty-selector');
                    if (specialtySelector) {
                      specialtySelector.disabled = false;
                    }
                  }
                } else {
                  console.error('Failed to load formations:', response.error);
                  showFormationError('Erreur lors du chargement des formations');
                }
              } catch (error) {
                console.error('Error initializing formations data:', error);
                showFormationError('Erreur de connexion à la base de données');
              }
            }

            // Charger les spécialités pour une formation
            async function loadSpecialitesForFormation(formationId, autoSelect = false) {
              try {
                console.log(`🔄 FORMATIONS: Loading specialites for formation ${formationId}...`);
                const response = await window.api.getFormationSpecialites(formationId);

                if (response && response.success) {
                  currentSpecialites = response.data;
                  console.log('Loaded specialites:', currentSpecialites);

                  const specialtySelector = document.getElementById('specialty-selector');
                  const specialtyContainer = specialtySelector.parentElement;

                  if (currentSpecialites.length > 0) {
                    // Show selector and populate with specialites
                    specialtySelector.style.display = 'inline-block';
                    specialtySelector.innerHTML = '<option value="">Sélectionner une spécialité...</option>';

                    currentSpecialites.forEach(specialite => {
                      const option = document.createElement('option');
                      option.value = specialite.id;
                      option.textContent = `${specialite.code} - ${specialite.nom}`;
                      specialtySelector.appendChild(option);
                    });

                    // Remove any existing placeholder
                    const existingPlaceholder = specialtyContainer.querySelector('.specialty-placeholder');
                    if (existingPlaceholder) {
                      existingPlaceholder.remove();
                    }

                    // Auto-select first specialite if requested
                    if (autoSelect) {
                      console.log('🔄 FORMATIONS: Auto-selecting first specialite:', currentSpecialites[0]);
                      specialtySelector.value = currentSpecialites[0].id;
                      currentSpecialite = currentSpecialites[0];
                      specialtySelector.disabled = false;

                      // Show formations interface and load dashboard data
                      showFormationsInterface();
                      await loadDashboardData();
                    } else {
                      specialtySelector.disabled = false;
                    }
                  } else {
                    // No specialites available - show placeholder
                    console.log('🔄 FORMATIONS: No specialites found, showing placeholder');
                    specialtySelector.style.display = 'none';

                    // Remove any existing placeholder first
                    const existingPlaceholder = specialtyContainer.querySelector('.specialty-placeholder');
                    if (existingPlaceholder) {
                      existingPlaceholder.remove();
                    }

                    // Create and add placeholder
                    const placeholder = document.createElement('div');
                    placeholder.className = 'specialty-placeholder';
                    placeholder.textContent = 'Aucune spécialité';
                    specialtyContainer.appendChild(placeholder);

                    // Reset current specialite
                    currentSpecialite = null;
                    hideFormationsInterface();
                  }
                } else {
                  console.error('Failed to load specialites:', response.error);
                  showFormationError('Erreur lors du chargement des spécialités');
                }
              } catch (error) {
                console.error('Error loading specialites:', error);
                showFormationError('Erreur de connexion à la base de données');
              }
            }

            // Charger les données du dashboard
            async function loadDashboardData() {
              if (!currentFormation || !currentSpecialite) return;

              try {
                console.log(`Loading dashboard data for formation ${currentFormation.id}, specialite ${currentSpecialite.id}...`);
                const response = await window.api.getFormationDashboard(currentFormation.id, currentSpecialite.id);

                if (response && response.success) {
                  const data = response.data;
                  console.log('Dashboard data loaded:', data);

                  // Mettre à jour les KPIs
                  document.getElementById('kpi-stagiaires').textContent = data.stagiaires || '0';
                  document.getElementById('kpi-fonctions').textContent = data.fonctions || '0';
                  document.getElementById('kpi-competences').textContent = data.competences || '0';
                  document.getElementById('kpi-heures').textContent = data.modules ? `${data.modules * 20}h` : '0h';

                  // Charger les données des autres sections
                  await loadStagiairesData();
                  await loadFonctionsData();
                } else {
                  console.error('Failed to load dashboard data:', response.error);
                  showFormationError('Erreur lors du chargement des données du tableau de bord');
                }
              } catch (error) {
                console.error('Error loading dashboard data:', error);
                showFormationError('Erreur de connexion à la base de données');
              }
            }

            // Charger les données des stagiaires
            async function loadStagiairesData() {
              if (!currentFormation || !currentSpecialite) return;

              try {
                console.log(`Loading stagiaires for formation ${currentFormation.id}, specialite ${currentSpecialite.id}...`);
                const response = await window.api.getFormationStagiaires(currentFormation.id, currentSpecialite.id);

                if (response && response.success) {
                  const stagiaires = response.data;
                  console.log('Stagiaires loaded:', stagiaires);

                  // Mettre à jour la liste des stagiaires
                  updateStagiairesList(stagiaires);
                } else {
                  console.error('Failed to load stagiaires:', response.error);
                }
              } catch (error) {
                console.error('Error loading stagiaires:', error);
              }
            }

            // Charger les données des fonctions
            async function loadFonctionsData() {
              if (!currentSpecialite) return;

              try {
                console.log(`Loading fonctions for specialite ${currentSpecialite.id}...`);
                const response = await window.api.getSpecialiteFonctions(currentSpecialite.id);

                if (response && response.success) {
                  const fonctions = response.data;
                  console.log('Fonctions loaded:', fonctions);

                  // Mettre à jour la grille des fonctions
                  updateFonctionsGrid(fonctions);
                } else {
                  console.error('Failed to load fonctions:', response.error);
                }
              } catch (error) {
                console.error('Error loading fonctions:', error);
              }
            }

            // Fonction pour afficher les erreurs
            function showFormationError(message) {
              console.error('Formation error:', message);
              // Créer une notification d'erreur simple
              const notification = document.createElement('div');
              notification.className = 'formations-error-notification';
              notification.textContent = message;
              notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #f44336;
                color: white;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
              `;

              document.body.appendChild(notification);

              // Supprimer après 5 secondes
              setTimeout(() => {
                if (document.body.contains(notification)) {
                  document.body.removeChild(notification);
                }
              }, 5000);
            }

            // Mettre à jour la liste des stagiaires
            function updateStagiairesList(stagiaires) {
              const stagiairesList = document.getElementById('stagiaires-list');
              if (!stagiairesList) return;

              stagiairesList.innerHTML = '';

              stagiaires.forEach(stagiaire => {
                const card = createStagiaireCard(stagiaire);
                stagiairesList.appendChild(card);
              });
            }

            // Mettre à jour la grille des fonctions
            function updateFonctionsGrid(fonctions) {
              const fonctionsGrid = document.getElementById('fonctions-grid');
              if (!fonctionsGrid) return;

              fonctionsGrid.innerHTML = '';

              fonctions.forEach(fonction => {
                const card = createFonctionCard(fonction);
                fonctionsGrid.appendChild(card);
              });
            }

            // Créer une carte de stagiaire
            function createStagiaireCard(stagiaire) {
              const card = document.createElement('div');
              card.className = 'formations-stagiaire-card';

              const progression = Math.round(stagiaire.progression_globale || 0);
              const progressionColor = progression >= 75 ? '#4CAF50' :
                                      progression >= 50 ? '#FF9800' : '#F44336';

              card.innerHTML = `
                <div class="formations-stagiaire-header">
                  <div class="formations-stagiaire-info">
                    <h3>${stagiaire.prenom} ${stagiaire.nom}</h3>
                    <span class="formations-stagiaire-matricule">Matricule: ${stagiaire.matricule}</span>
                    ${stagiaire.email ? `<span class="formations-stagiaire-email">${stagiaire.email}</span>` : ''}
                  </div>
                  <div class="formations-stagiaire-status ${stagiaire.statut}">
                    ${stagiaire.statut.replace('_', ' ').toUpperCase()}
                  </div>
                </div>
                <div class="formations-stagiaire-details">
                  <div class="formations-detail-item">
                    <span class="formations-detail-label">Date d'entrée:</span>
                    <span class="formations-detail-value">${new Date(stagiaire.date_entree).toLocaleDateString('fr-FR')}</span>
                  </div>
                  ${stagiaire.date_sortie_prevue ? `
                  <div class="formations-detail-item">
                    <span class="formations-detail-label">Sortie prévue:</span>
                    <span class="formations-detail-value">${new Date(stagiaire.date_sortie_prevue).toLocaleDateString('fr-FR')}</span>
                  </div>
                  ` : ''}
                  <div class="formations-detail-item">
                    <span class="formations-detail-label">Progression:</span>
                    <div class="formations-progress-container">
                      <div class="formations-progress-bar">
                        <div class="formations-progress-fill" style="width: ${progression}%; background-color: ${progressionColor}"></div>
                      </div>
                      <span class="formations-progress-text">${progression}%</span>
                    </div>
                  </div>
                </div>
              `;
              return card;
            }

            // Créer une carte de fonction
            function createFonctionCard(fonction) {
              const card = document.createElement('div');
              card.className = 'formations-fonction-card';

              const progression = Math.round(fonction.progression_moyenne || 0);
              const progressionColor = progression >= 75 ? '#4CAF50' :
                                      progression >= 50 ? '#FF9800' : '#F44336';

              card.innerHTML = `
                <div class="formations-fonction-header">
                  <h3>${fonction.code} - ${fonction.nom}</h3>
                  <span class="formations-fonction-competences">${fonction.nb_competences || 0} compétences</span>
                </div>
                <div class="formations-fonction-description">
                  ${fonction.description || 'Aucune description disponible'}
                </div>
                <div class="formations-fonction-stats">
                  <div class="formations-stat">
                    <span class="formations-stat-value">${fonction.nb_competences || 0}</span>
                    <span class="formations-stat-label">Compétences</span>
                  </div>
                  <div class="formations-stat">
                    <span class="formations-stat-value" style="color: ${progressionColor}">${progression}%</span>
                    <span class="formations-stat-label">Progression moy.</span>
                  </div>
                </div>
                <div class="formations-fonction-actions">
                  <button class="formations-btn formations-btn-small" onclick="loadCompetencesForFonction(${fonction.id}, '${fonction.nom}')">
                    <i class="fas fa-eye"></i> Voir Compétences
                  </button>
                </div>
              `;
              return card;
            }

            // Charger les compétences pour une fonction
            async function loadCompetencesForFonction(fonctionId, fonctionNom) {
              try {
                console.log(`Loading competences for fonction ${fonctionId}...`);
                const response = await window.api.getFonctionCompetences(fonctionId);

                if (response && response.success) {
                  const competences = response.data;
                  console.log('Competences loaded:', competences);

                  // Afficher les compétences dans une modal
                  showCompetencesModal(fonctionNom, competences);
                } else {
                  console.error('Failed to load competences:', response.error);
                  showFormationError('Erreur lors du chargement des compétences');
                }
              } catch (error) {
                console.error('Error loading competences:', error);
                showFormationError('Erreur de connexion à la base de données');
              }
            }

            // Afficher les compétences dans une modal
            function showCompetencesModal(fonctionNom, competences) {
              const modal = document.createElement('div');
              modal.className = 'formations-modal';
              modal.innerHTML = `
                <div class="formations-modal-content">
                  <div class="formations-modal-header">
                    <h3>Compétences - ${fonctionNom}</h3>
                    <button class="formations-modal-close">&times;</button>
                  </div>
                  <div class="formations-modal-body">
                    ${competences.length > 0 ? competences.map(comp => `
                      <div class="formations-competence-item">
                        <div class="formations-competence-header">
                          <strong>${comp.code} - ${comp.nom}</strong>
                          <span class="formations-competence-progress">${Math.round(comp.progression_moyenne || 0)}%</span>
                        </div>
                        <div class="formations-competence-description">${comp.description || 'Aucune description'}</div>
                        <div class="formations-competence-stats">
                          <small>${comp.nb_stagiaires_evalues || 0} stagiaires évalués</small>
                        </div>
                      </div>
                    `).join('') : '<p>Aucune compétence trouvée pour cette fonction.</p>'}
                  </div>
                </div>
              `;

              document.body.appendChild(modal);

              modal.querySelector('.formations-modal-close').addEventListener('click', () => {
                document.body.removeChild(modal);
              });

              modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                  document.body.removeChild(modal);
                }
              });
            }

            // Fonctions d'interface
            function showFormationsInterface() {
              const selectionMessage = document.getElementById('formations-selection-message');
              const formationsLayout = document.getElementById('formations-layout');
              if (selectionMessage) selectionMessage.style.display = 'none';
              if (formationsLayout) formationsLayout.style.display = 'flex';
            }

            function hideFormationsInterface() {
              const selectionMessage = document.getElementById('formations-selection-message');
              const formationsLayout = document.getElementById('formations-layout');
              if (selectionMessage) selectionMessage.style.display = 'flex';
              if (formationsLayout) formationsLayout.style.display = 'none';
            }

            // Configuration des event listeners pour les formations
            function setupFormationEventListeners() {
              console.log('🔧 FORMATIONS: Setting up event listeners...');
              const formationSelector = document.getElementById('formation-selector');
              const specialtySelector = document.getElementById('specialty-selector');

              console.log('🔧 FORMATIONS: Formation selector found:', !!formationSelector);
              console.log('🔧 FORMATIONS: Specialty selector found:', !!specialtySelector);

              if (formationSelector) {
                console.log('🔧 FORMATIONS: Adding change event listener to formation selector');
                formationSelector.addEventListener('change', async function() {
                  console.log('🔄 FORMATIONS: Formation selector changed, value:', this.value);
                  const formationId = parseInt(this.value);

                  if (formationId) {
                    console.log('🔄 FORMATIONS: Valid formation ID selected:', formationId);
                    currentFormation = allFormations.find(f => f.id === formationId);
                    console.log('🔄 FORMATIONS: Current formation set to:', currentFormation);

                    // Load specialites with auto-selection of first one
                    await loadSpecialitesForFormation(formationId, true);
                  } else {
                    console.log('🔄 FORMATIONS: No formation selected, resetting');
                    currentFormation = null;
                    currentSpecialite = null;
                    currentSpecialites = [];

                    // Reset specialty selector
                    if (specialtySelector) {
                      specialtySelector.style.display = 'inline-block';
                      specialtySelector.innerHTML = '<option value="">Sélectionner une spécialité...</option>';
                      specialtySelector.disabled = true;
                    }

                    // Remove any placeholder
                    const specialtyContainer = specialtySelector.parentElement;
                    const existingPlaceholder = specialtyContainer.querySelector('.specialty-placeholder');
                    if (existingPlaceholder) {
                      existingPlaceholder.remove();
                    }

                    hideFormationsInterface();
                  }
                });
              } else {
                console.error('❌ FORMATIONS: Formation selector not found!');
              }

              if (specialtySelector) {
                specialtySelector.addEventListener('change', function() {
                  const specialiteId = parseInt(this.value);

                  if (specialiteId && currentFormation) {
                    currentSpecialite = currentSpecialites.find(s => s.id === specialiteId);
                    showFormationsInterface();
                    loadDashboardData();
                  } else {
                    currentSpecialite = null;
                    hideFormationsInterface();
                  }
                });
              }

              // Setup navigation tabs event listeners
              console.log('🔧 FORMATIONS: Setting up navigation tabs event listeners...');
              const navItems = document.querySelectorAll('.formations-nav-item');
              console.log('🔧 FORMATIONS: Found', navItems.length, 'navigation items');

              navItems.forEach(item => {
                const section = item.getAttribute('data-section');
                console.log('🔧 FORMATIONS: Adding click listener for section:', section);

                item.addEventListener('click', function() {
                  console.log('🔄 FORMATIONS: Navigation tab clicked:', section);

                  // Remove active class from all nav items
                  navItems.forEach(navItem => navItem.classList.remove('active'));

                  // Add active class to clicked item
                  this.classList.add('active');

                  // Show corresponding section
                  showFormationSection(section);
                });
              });
            }

            // Afficher une section spécifique des formations
            function showFormationSection(sectionName) {
              console.log('🔄 FORMATIONS: Showing section:', sectionName);

              // Hide all sections first
              const sections = document.querySelectorAll('.formations-section');
              sections.forEach(section => {
                section.style.display = 'none';
              });

              // Show the requested section
              const targetSection = document.getElementById(`formations-${sectionName}`);
              if (targetSection) {
                targetSection.style.display = 'block';
                console.log('🔄 FORMATIONS: Section shown:', sectionName);
              } else {
                console.error('❌ FORMATIONS: Section not found:', `formations-${sectionName}`);
              }
            }

            // Configuration des event listeners pour les formations - configuré quand l'onglet est cliqué

            // Add click handler to map tab to refresh the map
            document.querySelectorAll('.tab').forEach(function (tab) {
              const tabName = tab.getAttribute('data-tab');

              if (tabName === 'map') {
                tab.addEventListener('click', function () {
                  console.log('Map tab clicked in direct script');
                  setTimeout(function () {
                    if (directMap) {
                      console.log('Refreshing map size in direct script');

                      // Ensure map is visible and properly sized
                      const mapElement = document.getElementById('morocco-map');
                      if (mapElement) {
                        // Force the map element to be visible
                        mapElement.style.display = 'block';
                        mapElement.style.height = '100%';
                        mapElement.style.width = '100%';
                        mapElement.style.position = 'relative';
                      }

                      // Invalidate size to fix any rendering issues
                      directMap.invalidateSize();

                      // Reset view to ensure Morocco is centered
                      directMap.setView(initialCenter, initialZoom);

                      // Update logo visibility based on toggle button state
                      const mapLogoOverlay = document.getElementById('map-logo-overlay');
                      const isAnyToggleActive = Array.from(document.querySelectorAll('.map-toggle-btn')).some(btn => btn.classList.contains('active'));

                      if (mapLogoOverlay) {
                        mapLogoOverlay.style.display = isAnyToggleActive ? 'none' : 'block';
                      }

                      // Refresh drone positions disabled as requested
                      console.log('Drone fetching and display disabled as requested');
                    }
                  }, 300);
                });
              } else if (tabName === 'formations') {
                // For formations tab, initialize formations data when clicked
                tab.addEventListener('click', async function() {
                  console.log('🎯 FORMATIONS: Tab clicked - initializing formations data');
                  if (droneRefreshInterval) {
                    console.log('Clearing drone refresh interval');
                    clearInterval(droneRefreshInterval);
                    droneRefreshInterval = null;
                  }
                  // Initialize formations data when tab is clicked
                  await initializeFormationsData();

                  // Setup event listeners after DOM elements are available
                  setTimeout(() => {
                    console.log('🔧 FORMATIONS: Setting up event listeners after DOM is ready...');
                    setupFormationEventListeners();
                  }, 100);
                });
              } else {
                // For other tabs, clear the drone refresh interval when switching away from map
                tab.addEventListener('click', function() {
                  if (droneRefreshInterval) {
                    console.log('Clearing drone refresh interval');
                    clearInterval(droneRefreshInterval);
                    droneRefreshInterval = null;
                  }
                });
              }
            });
          } catch (error) {
            console.error('Error in direct map initialization:', error);
          }
        }, 2000);
      });
    </script>

    <!-- Activity Details Modal -->
    <div id="activity-details-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="modal-activity-title">Détails de l'activité</h2>
          <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Activity navigation for multiple activities on same date -->
          <div id="modal-activity-nav" class="activity-nav">
            <!-- Will be populated dynamically -->
          </div>

          <div class="activity-details">
            <div class="detail-row">
              <div class="detail-label">Activité:</div>
              <div id="modal-activity-name" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de début:</div>
              <div id="modal-activity-start-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de fin:</div>
              <div id="modal-activity-end-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Durée:</div>
              <div id="modal-activity-duration" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Lieu:</div>
              <div id="modal-activity-location" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Référence:</div>
              <div id="modal-activity-reference" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Observations:</div>
              <div id="modal-activity-observations" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Source:</div>
              <div id="modal-activity-source" class="detail-value"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour tous les stagiaires de la formation -->
    <div id="all-trainees-modal" class="modal">
      <div class="modal-content all-trainees-modal-content">
        <div class="modal-header">
          <h2 id="all-trainees-modal-title">Tous les stagiaires - Formation</h2>
          <span class="close" id="close-all-trainees-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Barre de recherche et filtres -->
          <div class="modal-search-container">
            <div class="search-bar-modal">
              <input type="text" id="all-trainees-search" placeholder="Rechercher par nom, prénom, grade, unité, fonction..." />
              <button type="button" id="clear-search-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <!-- Filtre par instance -->
            <div class="instance-filter-container">
              <select id="all-trainees-instance-filter" class="instance-filter">
                <option value="">Toutes les instances</option>
              </select>
            </div>
            <!-- Filtre par fonction -->
            <div class="fonction-filter-container">
              <select id="all-trainees-fonction-filter" class="fonction-filter">
                <option value="">Toutes les fonctions</option>
              </select>
            </div>
          </div>
          <!-- Conteneur de la table -->
          <div class="all-trainees-table-container" id="all-trainees-table-container">
            <div class="loading-message">Chargement des stagiaires...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formations Initiales JavaScript -->
    <script>
      // Configuration des event listeners pour les formations - maintenant configurée dans la section principale

      // Les fonctions des formations initiales sont maintenant définies plus haut dans le fichier

      // Configuration des event listeners pour les formations - définie dans la section principale

      // Toutes les autres fonctions sont définies dans la section principale du script
    </script>
  </body>
</html>
          const breadcrumbFormationModules = document.getElementById('breadcrumb-formation-modules');
          const breadcrumbSpecialiteModules = document.getElementById('breadcrumb-specialite-modules');

          if (breadcrumbFormation && currentFormation) {
            breadcrumbFormation.textContent = currentFormation.code;
          }

          if (breadcrumbSpecialite && currentSpecialite) {
            breadcrumbSpecialite.textContent = currentSpecialite.code;
          }

          // Mise à jour des breadcrumbs pour la section modules
          if (breadcrumbFormationModules && currentFormation) {
            breadcrumbFormationModules.textContent = currentFormation.code;
          }

          if (breadcrumbSpecialiteModules && currentSpecialite) {
            breadcrumbSpecialiteModules.textContent = currentSpecialite.code;
          }
        }

        async function loadDashboardData() {
          if (!currentFormation || !currentSpecialite) return;

          try {
            console.log(`Loading dashboard data for formation ${currentFormation.id}, specialite ${currentSpecialite.id}...`);
            const response = await window.api.getFormationDashboard(currentFormation.id, currentSpecialite.id);

            if (response && response.success) {
              const data = response.data;
              console.log('Dashboard data loaded:', data);

              // Mettre à jour les KPIs
              document.getElementById('kpi-stagiaires').textContent = data.stagiaires || '0';
              document.getElementById('kpi-fonctions').textContent = data.fonctions || '0';
              document.getElementById('kpi-competences').textContent = data.competences || '0';
              document.getElementById('kpi-heures').textContent = data.modules ? `${data.modules * 20}h` : '0h'; // Estimation

              // Charger les données des autres sections
              await loadStagiairesData();
              await loadFonctionsData();
            } else {
              console.error('Failed to load dashboard data:', response.error);
              showFormationError('Erreur lors du chargement des données du tableau de bord');
            }
          } catch (error) {
            console.error('Error loading dashboard data:', error);
            showFormationError('Erreur de connexion à la base de données');
          }
        }

        function loadModulesForSpecialite() {
          if (!currentSpecialite) return;

          const modulesList = document.getElementById('modules-list');
          if (!modulesList) return;

          // Données simulées des modules par spécialité
          const mockModules = {
            1: [ // GNSS
              { id: 1, nom: 'GPS Fondamentaux', description: 'Formation aux bases du GPS', nbCours: 3, duree: 40 },
              { id: 2, nom: 'Navigation Avancée', description: 'Techniques de navigation avancées', nbCours: 3, duree: 35 },
              { id: 3, nom: 'Maintenance GPS', description: 'Maintenance des équipements GPS', nbCours: 2, duree: 25 }
            ],
            2: [ // DRONE
              { id: 4, nom: 'Théorie Drone', description: 'Principes de fonctionnement des drones', nbCours: 3, duree: 30 },
              { id: 5, nom: 'Pilotage Drone', description: 'Apprentissage du pilotage', nbCours: 3, duree: 50 },
              { id: 6, nom: 'Maintenance Drone', description: 'Maintenance des drones', nbCours: 3, duree: 40 }
            ],
            3: [ // SOUTIEN
              { id: 7, nom: 'Logistique', description: 'Techniques logistiques', nbCours: 2, duree: 35 },
              { id: 8, nom: 'Gestion Matériel', description: 'Gestion des stocks et équipements', nbCours: 2, duree: 30 },
              { id: 9, nom: 'Coordination', description: 'Coordination des opérations', nbCours: 2, duree: 25 }
            ]
          };

          const modules = mockModules[currentSpecialite.id] || [];
          modulesList.innerHTML = '';

          modules.forEach((module, index) => {
            const moduleItem = document.createElement('div');
            moduleItem.className = `formations-module-item ${index === 0 ? 'active' : ''}`;
            moduleItem.setAttribute('data-module', module.id);
            moduleItem.innerHTML = `
              <i class="fas fa-book"></i>
              <div class="formations-module-info">
                <span class="formations-module-name">${module.nom}</span>
                <span class="formations-module-details">${module.nbCours} cours • ${module.duree}h</span>
              </div>
            `;
            moduleItem.addEventListener('click', () => selectModule(module));
            modulesList.appendChild(moduleItem);
          });
        }

        function selectModule(module) {
          // Mettre à jour l'interface pour le module sélectionné
          const moduleItems = document.querySelectorAll('.formations-module-item');
          moduleItems.forEach(item => item.classList.remove('active'));

          const selectedItem = document.querySelector(`[data-module="${module.id}"]`);
          if (selectedItem) {
            selectedItem.classList.add('active');
          }

          console.log('Module sélectionné:', module);
          // Ici on chargerait les cours du module sélectionné
        }

        // Navigation entre sections
        const formationsNavItems = document.querySelectorAll('.formations-nav-item');
        const formationsSections = document.querySelectorAll('.formations-section');

        formationsNavItems.forEach(item => {
          item.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');

            // Remove active class from all nav items
            formationsNavItems.forEach(nav => nav.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');

            // Hide all sections
            formationsSections.forEach(section => section.classList.remove('active'));
            // Show selected section
            const targetSection = document.getElementById(`formations-${sectionId}`);
            if (targetSection) {
              targetSection.classList.add('active');
            }
          });
        });

        // Gestion des boutons d'ajout
        const addFonctionBtn = document.getElementById('add-fonction-btn');
        const addStagiaireBtn = document.getElementById('add-stagiaire-btn');
        const addCompetenceBtn = document.getElementById('add-competence-btn');
        const addCoursBtn = document.getElementById('add-cours-btn');

        if (addFonctionBtn) {
          addFonctionBtn.addEventListener('click', function() {
            console.log('Add fonction clicked');
            // Ici on ouvrirait un modal pour ajouter une fonction
          });
        }

        if (addStagiaireBtn) {
          addStagiaireBtn.addEventListener('click', function() {
            console.log('Add stagiaire clicked');
          });
        }

        if (addCompetenceBtn) {
          addCompetenceBtn.addEventListener('click', function() {
            console.log('Add competence clicked');
          });
        }

        if (addCoursBtn) {
          addCoursBtn.addEventListener('click', function() {
            console.log('Add cours clicked');
          });
        }
      });

      // Charger les données des stagiaires
      async function loadStagiairesData() {
        if (!currentFormation || !currentSpecialite) return;

        try {
          console.log(`Loading stagiaires for formation ${currentFormation.id}, specialite ${currentSpecialite.id}...`);
          const response = await window.api.getFormationStagiaires(currentFormation.id, currentSpecialite.id);

          if (response && response.success) {
            const stagiaires = response.data;
            console.log('Stagiaires loaded:', stagiaires);

            // Mettre à jour la liste des stagiaires
            updateStagiairesList(stagiaires);
          } else {
            console.error('Failed to load stagiaires:', response.error);
          }
        } catch (error) {
          console.error('Error loading stagiaires:', error);
        }
      }

      // Charger les données des fonctions
      async function loadFonctionsData() {
        if (!currentSpecialite) return;

        try {
          console.log(`Loading fonctions for specialite ${currentSpecialite.id}...`);
          const response = await window.api.getSpecialiteFonctions(currentSpecialite.id);

          if (response && response.success) {
            const fonctions = response.data;
            console.log('Fonctions loaded:', fonctions);

            // Mettre à jour la grille des fonctions
            updateFonctionsGrid(fonctions);
          } else {
            console.error('Failed to load fonctions:', response.error);
          }
        } catch (error) {
          console.error('Error loading fonctions:', error);
        }
      }

      // Mettre à jour la liste des stagiaires
      function updateStagiairesList(stagiaires) {
        const stagiairesList = document.getElementById('stagiaires-list');
        if (!stagiairesList) return;

        stagiairesList.innerHTML = '';

        stagiaires.forEach(stagiaire => {
          const card = createStagiaireCard(stagiaire);
          stagiairesList.appendChild(card);
        });
      }

      // Mettre à jour la grille des fonctions
      function updateFonctionsGrid(fonctions) {
        const fonctionsGrid = document.getElementById('fonctions-grid');
        if (!fonctionsGrid) return;

        fonctionsGrid.innerHTML = '';

        fonctions.forEach(fonction => {
          const card = createFonctionCard(fonction);
          fonctionsGrid.appendChild(card);
        });
      }

      // Créer une carte de stagiaire
      function createStagiaireCard(stagiaire) {
        const card = document.createElement('div');
        card.className = 'formations-stagiaire-card';

        const progression = Math.round(stagiaire.progression_globale || 0);
        const progressionColor = progression >= 75 ? '#4CAF50' :
                                progression >= 50 ? '#FF9800' : '#F44336';

        card.innerHTML = `
          <div class="formations-stagiaire-header">
            <div class="formations-stagiaire-info">
              <h3>${stagiaire.prenom} ${stagiaire.nom}</h3>
              <span class="formations-stagiaire-matricule">Matricule: ${stagiaire.matricule}</span>
              ${stagiaire.email ? `<span class="formations-stagiaire-email">${stagiaire.email}</span>` : ''}
            </div>
            <div class="formations-stagiaire-status ${stagiaire.statut}">
              ${stagiaire.statut.replace('_', ' ').toUpperCase()}
            </div>
          </div>
          <div class="formations-stagiaire-details">
            <div class="formations-detail-item">
              <span class="formations-detail-label">Date d'entrée:</span>
              <span class="formations-detail-value">${new Date(stagiaire.date_entree).toLocaleDateString('fr-FR')}</span>
            </div>
            ${stagiaire.date_sortie_prevue ? `
            <div class="formations-detail-item">
              <span class="formations-detail-label">Sortie prévue:</span>
              <span class="formations-detail-value">${new Date(stagiaire.date_sortie_prevue).toLocaleDateString('fr-FR')}</span>
            </div>
            ` : ''}
            <div class="formations-detail-item">
              <span class="formations-detail-label">Progression:</span>
              <div class="formations-progress-container">
                <div class="formations-progress-bar">
                  <div class="formations-progress-fill" style="width: ${progression}%; background-color: ${progressionColor};"></div>
                </div>
                <span class="formations-progress-text">${progression}%</span>
              </div>
            </div>
          </div>
        `;
        return card;
      }

      // Créer une carte de fonction (mise à jour pour utiliser les données de la DB)
      function createFonctionCard(fonction) {
        const card = document.createElement('div');
        card.className = 'formations-fonction-card';

        const progression = Math.round(fonction.progression_moyenne || 0);
        const progressionColor = progression >= 75 ? '#4CAF50' :
                                progression >= 50 ? '#FF9800' : '#F44336';

        card.innerHTML = `
          <div class="formations-fonction-header">
            <h3>${fonction.code} - ${fonction.nom}</h3>
            <span class="formations-fonction-competences">${fonction.nb_competences || 0} compétences</span>
          </div>
          <div class="formations-fonction-description">
            ${fonction.description || 'Aucune description disponible'}
          </div>
          <div class="formations-fonction-stats">
            <div class="formations-stat">
              <span class="formations-stat-value">${fonction.nb_competences || 0}</span>
              <span class="formations-stat-label">Compétences</span>
            </div>
            <div class="formations-stat">
              <span class="formations-stat-value" style="color: ${progressionColor}">${progression}%</span>
              <span class="formations-stat-label">Progression moy.</span>
            </div>
          </div>
          <div class="formations-fonction-actions">
            <button class="formations-btn formations-btn-small" onclick="loadCompetencesForFonction(${fonction.id}, '${fonction.nom}')">
              <i class="fas fa-eye"></i> Voir Compétences
            </button>
          </div>
        `;
        return card;
      }

      // Charger les compétences pour une fonction
      async function loadCompetencesForFonction(fonctionId, fonctionNom) {
        try {
          console.log(`Loading competences for fonction ${fonctionId}...`);
          const response = await window.api.getFonctionCompetences(fonctionId);

          if (response && response.success) {
            const competences = response.data;
            console.log('Competences loaded:', competences);

            // Afficher les compétences dans une modal
            showCompetencesModal(fonctionNom, competences);
          } else {
            console.error('Failed to load competences:', response.error);
            showFormationError('Erreur lors du chargement des compétences');
          }
        } catch (error) {
          console.error('Error loading competences:', error);
          showFormationError('Erreur de connexion à la base de données');
        }
      }

      // Afficher les compétences dans une modal
      function showCompetencesModal(fonctionNom, competences) {
        // Créer une modal simple pour afficher les compétences
        const modal = document.createElement('div');
        modal.className = 'formations-modal';
        modal.innerHTML = `
          <div class="formations-modal-content">
            <div class="formations-modal-header">
              <h3>Compétences - ${fonctionNom}</h3>
              <button class="formations-modal-close">&times;</button>
            </div>
            <div class="formations-modal-body">
              ${competences.length > 0 ? competences.map(comp => `
                <div class="formations-competence-item">
                  <div class="formations-competence-header">
                    <strong>${comp.code} - ${comp.nom}</strong>
                    <span class="formations-competence-progress">${Math.round(comp.progression_moyenne || 0)}%</span>
                  </div>
                  <div class="formations-competence-description">${comp.description || 'Aucune description'}</div>
                  <div class="formations-competence-stats">
                    <small>${comp.nb_stagiaires_evalues || 0} stagiaires évalués</small>
                  </div>
                </div>
              `).join('') : '<p>Aucune compétence trouvée pour cette fonction.</p>'}
            </div>
          </div>
        `;

        // Ajouter la modal au DOM
        document.body.appendChild(modal);

        // Event listener pour fermer la modal
        modal.querySelector('.formations-modal-close').addEventListener('click', () => {
          document.body.removeChild(modal);
        });

        // Fermer en cliquant à l'extérieur
        modal.addEventListener('click', (e) => {
          if (e.target === modal) {
            document.body.removeChild(modal);
          }
        });
      }

      // Fonction pour afficher les erreurs
      function showFormationError(message) {
        console.error('Formation error:', message);
        // Créer une notification d'erreur simple
        const notification = document.createElement('div');
        notification.className = 'formations-error-notification';
        notification.textContent = message;
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #f44336;
          color: white;
          padding: 12px 20px;
          border-radius: 4px;
          z-index: 10000;
          box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        document.body.appendChild(notification);

        // Supprimer après 5 secondes
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 5000);
      }

      function viewStagiaire(stagiaireId) {
        console.log(`Viewing stagiaire: ${stagiaireId}`);
      }

      function editStagiaire(stagiaireId) {
        console.log(`Editing stagiaire: ${stagiaireId}`);
      }
    </script>
  </body>
</html>
